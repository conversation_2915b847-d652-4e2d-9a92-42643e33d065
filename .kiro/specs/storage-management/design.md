# Design Document: Storage Management System

## Overview

The Storage Management System will provide users with a comprehensive way to view, manage, and delete media files to free up storage space. The system will track storage usage, enforce limits, and provide notifications when users approach their storage limits. This design follows a similar approach to WhatsApp's storage management system.

## Architecture

The system will be implemented using a layered architecture:

1. **Frontend Layer**: Flutter UI components for displaying storage usage, file listings, and management options
2. **API Layer**: Backend endpoints for retrieving storage information and managing files
3. **Service Layer**: Business logic for calculating storage usage, enforcing limits, and managing files
4. **Data Layer**: Database models for tracking user storage metadata and file information

### System Components Diagram

```mermaid
graph TD
    A[User Interface] --> B[Storage API Controller]
    B --> C[Storage Service]
    C --> D[File Service]
    C --> E[Message Service]
    C --> F[Notification Service]
    D --> G[File Storage]
    E --> H[Message Database]
    C --> I[Storage Metadata Database]
```

## Components and Interfaces

### 1. Storage Models

#### StorageMetadata Model

```typescript
interface IStorageMetadata {
  _id: string;
  userId: string;
  totalUsage: number; // in bytes
  imageUsage: number;
  videoUsage: number;
  documentUsage: number;
  voiceUsage: number;
  lastCalculatedAt: Date;
  createdAt: Date;
  updatedAt: Date;
}
```

#### FileMetadata Model

```typescript
interface IFileMetadata {
  _id: string;
  userId: string;
  fileKey: string;
  fileName: string;
  fileType: string; // mime type
  fileSize: number; // in bytes
  mediaType: string; // image, video, document, voice
  messageId: string; // reference to the message containing this file
  roomId: string; // chat room where the file was shared
  senderId: string; // user who sent the file
  receiverId: string; // user who received the file (for single chats)
  sentAt: Date;
  createdAt: Date;
  updatedAt: Date;
}
```

### 2. API Endpoints

#### Storage Controller

```typescript
@Controller('storage')
export class StorageController {
  @Get('usage')
  getStorageUsage(@Req() req): Promise<StorageUsageDto>
  
  @Get('files')
  getFiles(@Req() req, @Query() query: GetFilesDto): Promise<PaginatedFilesDto>
  
  @Delete('files')
  deleteFiles(@Req() req, @Body() body: DeleteFilesDto): Promise<DeleteResultDto>
  
  @Get('premium')
  getPremiumOptions(@Req() req): Promise<PremiumOptionsDto>
}
```

### 3. Services

#### Storage Service

```typescript
@Injectable()
export class StorageService {
  calculateUserStorage(userId: string): Promise<StorageUsageDto>
  
  getFilesByType(userId: string, fileType: string, options: FilterOptions): Promise<PaginatedFilesDto>
  
  deleteFiles(userId: string, fileIds: string[]): Promise<DeleteResultDto>
  
  checkStorageLimit(userId: string): Promise<StorageLimitStatus>
  
  updateStorageUsage(userId: string): Promise<void>
}
```

#### Storage Notification Service

```typescript
@Injectable()
export class StorageNotificationService {
  checkAndSendNotifications(): Promise<void>
  
  sendStorageNotification(userId: string, usagePercentage: number): Promise<void>
}
```

## Data Models

### Database Schema

The system will use MongoDB with the following collections:

1. **storage_metadata**: Tracks overall storage usage for each user
2. **file_metadata**: Tracks individual file information for storage management
3. **messages**: Existing collection, will be updated when files are deleted

### Data Flow

1. When a user uploads a file:
   - File is stored in the file system or cloud storage
   - File metadata is recorded in the file_metadata collection
   - User's storage usage is updated in storage_metadata

2. When a user deletes files:
   - Files are removed from storage
   - File metadata records are deleted
   - References in messages are updated
   - User's storage usage is recalculated

## Error Handling

### Storage Limit Errors

When a user reaches their storage limit:

1. Upload attempts will be rejected with a 403 Forbidden status
2. Error response will include:
   - Current storage usage
   - Storage limit
   - Suggestion to delete files or upgrade

### File Deletion Errors

If file deletion fails:

1. Return appropriate error codes based on the failure reason
2. Implement retry logic for cloud storage operations
3. Maintain transaction integrity to avoid orphaned records

## Testing Strategy

### Unit Tests

1. Test storage calculation logic
2. Test file deletion logic
3. Test notification threshold logic

### Integration Tests

1. Test file upload and storage update flow
2. Test file deletion and message update flow
3. Test notification sending based on storage thresholds

### End-to-End Tests

1. Test complete user flow from upload to storage management
2. Test bulk deletion functionality
3. Test storage limit enforcement

## Implementation Plan

### Backend Implementation

1. Create storage_metadata and file_metadata collections
2. Implement StorageService for tracking and managing storage
3. Implement API endpoints for retrieving and managing storage
4. Implement notification service for storage alerts
5. Update file upload process to track storage usage
6. Update message service to handle file deletions

### Frontend Implementation

1. Create storage usage overview screen
2. Implement media type breakdown views
3. Create file listing and selection interface
4. Implement bulk deletion functionality
5. Add premium upgrade placeholder screen
6. Integrate with notification system

## Security Considerations

1. Ensure users can only access and delete their own files
2. Validate file ownership before deletion
3. Implement rate limiting on storage-related API endpoints
4. Sanitize file metadata to prevent injection attacks

## Performance Considerations

1. Use pagination for file listings to handle large numbers of files
2. Implement caching for storage usage calculations
3. Use background jobs for recalculating storage after bulk operations
4. Optimize queries for file metadata retrieval