// Copyright 2023, the hatemragab project author.
// All rights reserved. Use of this source code is governed by a
// MIT license that can be found in the LICENSE file.

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../controllers/storage_controller.dart';

class StorageBinding {
  static Widget wrapWithProvider(Widget child) {
    return ChangeNotifierProvider(
      create: (context) => StorageController(),
      child: child,
    );
  }
}