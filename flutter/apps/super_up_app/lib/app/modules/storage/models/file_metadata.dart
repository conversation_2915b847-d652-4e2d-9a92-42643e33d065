// Copyright 2023, the hate<PERSON>ragab project author.
// All rights reserved. Use of this source code is governed by a
// MIT license that can be found in the LICENSE file.

class FileMetadata {
  final String id;
  final String name;
  final String path;
  final String mimeType;
  final double size; // in bytes
  final DateTime createdAt;
  final DateTime? lastAccessed;
  final String? thumbnailUrl;
  final String? url;
  final Map<String, dynamic>? metadata;

  FileMetadata({
    required this.id,
    required this.name,
    required this.path,
    required this.mimeType,
    required this.size,
    required this.createdAt,
    this.lastAccessed,
    this.thumbnailUrl,
    this.url,
    this.metadata,
  });

  bool get isImage => mimeType.startsWith('image/');
  bool get isVideo => mimeType.startsWith('video/');
  bool get isAudio => mimeType.startsWith('audio/');
  bool get isDocument => !isImage && !isVideo && !isAudio;

  factory FileMetadata.fromJson(Map<String, dynamic> json) {
    return FileMetadata(
      id: json['id'],
      name: json['name'],
      path: json['path'],
      mimeType: json['mimeType'],
      size: json['size'].toDouble(),
      createdAt: DateTime.parse(json['createdAt']),
      lastAccessed: json['lastAccessed'] != null 
          ? DateTime.parse(json['lastAccessed']) 
          : null,
      thumbnailUrl: json['thumbnailUrl'],
      url: json['url'],
      metadata: json['metadata'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'path': path,
      'mimeType': mimeType,
      'size': size,
      'createdAt': createdAt.toIso8601String(),
      'lastAccessed': lastAccessed?.toIso8601String(),
      'thumbnailUrl': thumbnailUrl,
      'url': url,
      'metadata': metadata,
    };
  }
}