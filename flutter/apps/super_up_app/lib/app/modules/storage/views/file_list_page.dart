// Copyright 2023, the hate<PERSON><PERSON><PERSON> project author.
// All rights reserved. Use of this source code is governed by a
// MIT license that can be found in the LICENSE file.

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import '../controllers/storage_controller.dart';
import '../models/file_metadata.dart';
import 'file_detail_page.dart';

enum FileListType { images, videos, documents, voice }

enum SortOption {
  sizeDesc,
  sizeAsc,
  dateDesc,
  dateAsc,
  nameAsc,
  nameDesc,
}

class FileListPage extends StatefulWidget {
  final FileListType type;
  final String title;
  final Color accentColor;
  final IconData icon;
  final StorageController controller;

  const FileListPage({
    super.key,
    required this.type,
    required this.title,
    required this.accentColor,
    required this.icon,
    required this.controller,
  });

  @override
  State<FileListPage> createState() => _FileListPageState();
}

class _FileListPageState extends State<FileListPage> {
  bool _isSelectionMode = false;
  final Set<String> _selectedFiles = {};
  SortOption _currentSort = SortOption.sizeDesc; // Default: largest first

  @override
  void initState() {
    super.initState();
    _loadFiles();
  }

  Future<void> _loadFiles() async {
    final controller = widget.controller;
    switch (widget.type) {
      case FileListType.images:
        if (controller.imageFiles.isEmpty) {
          await controller.fetchImageFiles();
        }
        break;
      case FileListType.videos:
        if (controller.videoFiles.isEmpty) {
          await controller.fetchVideoFiles();
        }
        break;
      case FileListType.documents:
        if (controller.documentFiles.isEmpty) {
          await controller.fetchDocumentFiles();
        }
        break;
      case FileListType.voice:
        if (controller.voiceFiles.isEmpty) {
          await controller.fetchVoiceFiles();
        }
        break;
    }
  }

  List<FileMetadata> _getFiles(StorageController controller) {
    List<FileMetadata> files;
    switch (widget.type) {
      case FileListType.images:
        files = List.from(controller.imageFiles);
        break;
      case FileListType.videos:
        files = List.from(controller.videoFiles);
        break;
      case FileListType.documents:
        files = List.from(controller.documentFiles);
        break;
      case FileListType.voice:
        files = List.from(controller.voiceFiles);
        break;
    }

    // Apply sorting
    return _sortFiles(files);
  }

  List<FileMetadata> _sortFiles(List<FileMetadata> files) {
    switch (_currentSort) {
      case SortOption.sizeDesc:
        files.sort((a, b) => b.size.compareTo(a.size));
        break;
      case SortOption.sizeAsc:
        files.sort((a, b) => a.size.compareTo(b.size));
        break;
      case SortOption.dateDesc:
        files.sort((a, b) => b.createdAt.compareTo(a.createdAt));
        break;
      case SortOption.dateAsc:
        files.sort((a, b) => a.createdAt.compareTo(b.createdAt));
        break;
      case SortOption.nameAsc:
        files.sort(
            (a, b) => a.name.toLowerCase().compareTo(b.name.toLowerCase()));
        break;
      case SortOption.nameDesc:
        files.sort(
            (a, b) => b.name.toLowerCase().compareTo(a.name.toLowerCase()));
        break;
    }
    return files;
  }

  bool _getIsLoading(StorageController controller) {
    switch (widget.type) {
      case FileListType.images:
        return controller.isLoadingImages;
      case FileListType.videos:
        return controller.isLoadingVideos;
      case FileListType.documents:
        return controller.isLoadingDocuments;
      case FileListType.voice:
        return controller.isLoadingVoice;
    }
  }

  void _toggleSelectionMode() {
    setState(() {
      _isSelectionMode = !_isSelectionMode;
      if (!_isSelectionMode) {
        _selectedFiles.clear();
      }
    });
  }

  void _toggleFileSelection(String fileId) {
    setState(() {
      if (_selectedFiles.contains(fileId)) {
        _selectedFiles.remove(fileId);
      } else {
        _selectedFiles.add(fileId);
      }

      if (_selectedFiles.isEmpty && _isSelectionMode) {
        _isSelectionMode = false;
      }
    });
  }

  void _selectAllFiles(List<FileMetadata> files) {
    setState(() {
      if (_selectedFiles.length == files.length) {
        // If all files are selected, deselect all
        _selectedFiles.clear();
      } else {
        // Select all files
        _selectedFiles.clear();
        _selectedFiles.addAll(files.map((file) => file.id));
      }
    });
  }

  Future<void> _deleteSelectedFiles(
      List<FileMetadata> files, StorageController controller) async {
    final filesToDelete =
        files.where((file) => _selectedFiles.contains(file.id)).toList();
    final totalSize = filesToDelete.fold(0.0, (sum, file) => sum + file.size);

    // Show confirmation dialog with size information
    final shouldDelete = await showCupertinoDialog<bool>(
      context: context,
      builder: (context) => CupertinoAlertDialog(
        title: const Text('Delete Files'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
                'Are you sure you want to delete ${filesToDelete.length} ${filesToDelete.length == 1 ? 'file' : 'files'}?'),
            const SizedBox(height: 8),
            Text(
              'Total size: ${controller.formatBytes(totalSize)}',
              style: const TextStyle(
                fontWeight: FontWeight.w600,
                color: CupertinoColors.systemBlue,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              'This action cannot be undone.',
              style: TextStyle(
                color: CupertinoColors.systemGrey,
                fontSize: 14,
              ),
            ),
          ],
        ),
        actions: [
          CupertinoDialogAction(
            isDestructiveAction: true,
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('Delete'),
          ),
          CupertinoDialogAction(
            isDefaultAction: true,
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );

    if (shouldDelete == true) {
      // Show loading indicator
      showCupertinoDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const CupertinoAlertDialog(
          title: Text('Deleting Files'),
          content: Padding(
            padding: EdgeInsets.symmetric(vertical: 20),
            child: CupertinoActivityIndicator(),
          ),
        ),
      );

      // Delete files one by one
      for (final file in filesToDelete) {
        await controller.deleteFile(file);
      }

      // Dismiss loading dialog
      Navigator.of(context).pop();

      // Exit selection mode
      setState(() {
        _isSelectionMode = false;
        _selectedFiles.clear();
      });
    }
  }

  void _showSortOptions() {
    showCupertinoModalPopup(
      context: context,
      builder: (context) => CupertinoActionSheet(
        title: const Text('Sort Files'),
        actions: [
          CupertinoActionSheetAction(
            onPressed: () {
              setState(() {
                _currentSort = SortOption.sizeDesc;
              });
              Navigator.pop(context);
            },
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text('Size (Largest First)'),
                if (_currentSort == SortOption.sizeDesc)
                  const Icon(CupertinoIcons.checkmark,
                      color: CupertinoColors.activeBlue),
              ],
            ),
          ),
          CupertinoActionSheetAction(
            onPressed: () {
              setState(() {
                _currentSort = SortOption.sizeAsc;
              });
              Navigator.pop(context);
            },
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text('Size (Smallest First)'),
                if (_currentSort == SortOption.sizeAsc)
                  const Icon(CupertinoIcons.checkmark,
                      color: CupertinoColors.activeBlue),
              ],
            ),
          ),
          CupertinoActionSheetAction(
            onPressed: () {
              setState(() {
                _currentSort = SortOption.dateDesc;
              });
              Navigator.pop(context);
            },
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text('Date (Newest First)'),
                if (_currentSort == SortOption.dateDesc)
                  const Icon(CupertinoIcons.checkmark,
                      color: CupertinoColors.activeBlue),
              ],
            ),
          ),
          CupertinoActionSheetAction(
            onPressed: () {
              setState(() {
                _currentSort = SortOption.dateAsc;
              });
              Navigator.pop(context);
            },
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text('Date (Oldest First)'),
                if (_currentSort == SortOption.dateAsc)
                  const Icon(CupertinoIcons.checkmark,
                      color: CupertinoColors.activeBlue),
              ],
            ),
          ),
          CupertinoActionSheetAction(
            onPressed: () {
              setState(() {
                _currentSort = SortOption.nameAsc;
              });
              Navigator.pop(context);
            },
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text('Name (A to Z)'),
                if (_currentSort == SortOption.nameAsc)
                  const Icon(CupertinoIcons.checkmark,
                      color: CupertinoColors.activeBlue),
              ],
            ),
          ),
          CupertinoActionSheetAction(
            onPressed: () {
              setState(() {
                _currentSort = SortOption.nameDesc;
              });
              Navigator.pop(context);
            },
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text('Name (Z to A)'),
                if (_currentSort == SortOption.nameDesc)
                  const Icon(CupertinoIcons.checkmark,
                      color: CupertinoColors.activeBlue),
              ],
            ),
          ),
        ],
        cancelButton: CupertinoActionSheetAction(
          onPressed: () => Navigator.pop(context),
          child: const Text('Cancel'),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: widget.controller,
      builder: (context, child) {
        final controller = widget.controller;
        final files = _getFiles(controller);
        final isLoading = _getIsLoading(controller);

        return CupertinoPageScaffold(
          navigationBar: CupertinoNavigationBar(
            middle: _isSelectionMode
                ? Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        '${_selectedFiles.length} selected',
                        style: const TextStyle(fontSize: 17),
                      ),
                      if (_selectedFiles.isNotEmpty)
                        Text(
                          controller.formatBytes(
                            files
                                .where(
                                    (file) => _selectedFiles.contains(file.id))
                                .fold(0.0, (sum, file) => sum + file.size),
                          ),
                          style: const TextStyle(
                            fontSize: 12,
                            color: CupertinoColors.systemGrey,
                          ),
                        ),
                    ],
                  )
                : Text(widget.title),
            trailing: _isSelectionMode
                ? CupertinoButton(
                    padding: EdgeInsets.zero,
                    child: const Text('Cancel'),
                    onPressed: _toggleSelectionMode,
                  )
                : Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      CupertinoButton(
                        padding: EdgeInsets.zero,
                        child: const Icon(
                          CupertinoIcons.sort_down,
                          size: 22,
                        ),
                        onPressed: files.isNotEmpty ? _showSortOptions : null,
                      ),
                      CupertinoButton(
                        padding: EdgeInsets.zero,
                        child: const Text('Select'),
                        onPressed:
                            files.isNotEmpty ? _toggleSelectionMode : null,
                      ),
                    ],
                  ),
            leading: _isSelectionMode
                ? CupertinoButton(
                    padding: EdgeInsets.zero,
                    child: const Icon(
                      CupertinoIcons.delete,
                      color: CupertinoColors.destructiveRed,
                    ),
                    onPressed: _selectedFiles.isNotEmpty
                        ? () => _deleteSelectedFiles(files, controller)
                        : null,
                  )
                : null,
          ),
          child: SafeArea(
            child: isLoading
                ? const Center(
                    child: CupertinoActivityIndicator(),
                  )
                : files.isEmpty
                    ? Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              widget.icon,
                              size: 64,
                              color: CupertinoColors.systemGrey,
                            ),
                            const SizedBox(height: 16),
                            Text(
                              'No ${widget.title.toLowerCase()} found',
                              style: const TextStyle(
                                fontSize: 18,
                                color: CupertinoColors.systemGrey,
                              ),
                            ),
                            const SizedBox(height: 24),
                            CupertinoButton(
                              onPressed: _loadFiles,
                              child: const Text('Refresh'),
                            ),
                          ],
                        ),
                      )
                    : Column(
                        children: [
                          Padding(
                            padding: const EdgeInsets.all(16),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(
                                  '${files.length} ${widget.title.toLowerCase()}',
                                  style: const TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                                Text(
                                  'Total: ${controller.formatBytes(files.fold(0.0, (sum, file) => sum + file.size))}',
                                  style: const TextStyle(
                                    fontSize: 14,
                                    color: CupertinoColors.systemGrey,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          Expanded(
                            child: ListView.builder(
                              itemCount: files.length,
                              itemBuilder: (context, index) {
                                final file = files[index];
                                final isSelected =
                                    _selectedFiles.contains(file.id);

                                return GestureDetector(
                                  onTap: _isSelectionMode
                                      ? () => _toggleFileSelection(file.id)
                                      : () => Navigator.of(context).push(
                                            CupertinoPageRoute(
                                              builder: (context) =>
                                                  FileDetailPage(
                                                file: file,
                                                controller: controller,
                                              ),
                                            ),
                                          ),
                                  onLongPress: () {
                                    if (!_isSelectionMode) {
                                      _toggleSelectionMode();
                                      _toggleFileSelection(file.id);
                                    }
                                  },
                                  child: Container(
                                    color: isSelected
                                        ? widget.accentColor
                                            .withValues(alpha: 0.1)
                                        : null,
                                    child: Padding(
                                      padding: const EdgeInsets.symmetric(
                                        horizontal: 16,
                                        vertical: 12,
                                      ),
                                      child: Row(
                                        children: [
                                          _buildFilePreview(file),
                                          const SizedBox(width: 16),
                                          Expanded(
                                            child: Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                Text(
                                                  file.name,
                                                  style: const TextStyle(
                                                    fontSize: 16,
                                                    fontWeight: FontWeight.w500,
                                                  ),
                                                  maxLines: 1,
                                                  overflow:
                                                      TextOverflow.ellipsis,
                                                ),
                                                const SizedBox(height: 4),
                                                Row(
                                                  children: [
                                                    Text(
                                                      controller.formatBytes(
                                                          file.size),
                                                      style: const TextStyle(
                                                        fontSize: 14,
                                                        color: CupertinoColors
                                                            .systemGrey,
                                                      ),
                                                    ),
                                                    const SizedBox(width: 8),
                                                    const Text(
                                                      '•',
                                                      style: TextStyle(
                                                        color: CupertinoColors
                                                            .systemGrey,
                                                      ),
                                                    ),
                                                    const SizedBox(width: 8),
                                                    Text(
                                                      _formatDate(
                                                          file.createdAt),
                                                      style: const TextStyle(
                                                        fontSize: 14,
                                                        color: CupertinoColors
                                                            .systemGrey,
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ],
                                            ),
                                          ),
                                          if (_isSelectionMode)
                                            Icon(
                                              isSelected
                                                  ? CupertinoIcons
                                                      .checkmark_circle_fill
                                                  : CupertinoIcons.circle,
                                              color: isSelected
                                                  ? widget.accentColor
                                                  : CupertinoColors.systemGrey,
                                            )
                                          else
                                            const Icon(
                                              CupertinoIcons.chevron_right,
                                              color: CupertinoColors.systemGrey,
                                              size: 18,
                                            ),
                                        ],
                                      ),
                                    ),
                                  ),
                                );
                              },
                            ),
                          ),
                        ],
                      ),
          ),
        );
      },
    );
  }

  Widget _buildFilePreview(FileMetadata file) {
    switch (widget.type) {
      case FileListType.images:
        return Container(
          width: 56,
          height: 56,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            image: file.thumbnailUrl != null
                ? DecorationImage(
                    image: NetworkImage(file.thumbnailUrl!),
                    fit: BoxFit.cover,
                  )
                : null,
            color: CupertinoColors.systemGrey6,
          ),
          child: file.thumbnailUrl == null
              ? const Icon(
                  CupertinoIcons.photo,
                  color: CupertinoColors.systemGrey,
                )
              : null,
        );
      case FileListType.videos:
        return Stack(
          children: [
            Container(
              width: 56,
              height: 56,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                image: file.thumbnailUrl != null
                    ? DecorationImage(
                        image: NetworkImage(file.thumbnailUrl!),
                        fit: BoxFit.cover,
                      )
                    : null,
                color: CupertinoColors.systemGrey6,
              ),
              child: file.thumbnailUrl == null
                  ? const Icon(
                      CupertinoIcons.video_camera,
                      color: CupertinoColors.systemGrey,
                    )
                  : null,
            ),
            Positioned(
              bottom: 4,
              right: 4,
              child: Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 4,
                  vertical: 2,
                ),
                decoration: BoxDecoration(
                  color: CupertinoColors.black.withOpacity(0.7),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(
                  file.metadata?['duration'] ?? '0:00',
                  style: const TextStyle(
                    color: CupertinoColors.white,
                    fontSize: 10,
                  ),
                ),
              ),
            ),
          ],
        );
      case FileListType.documents:
        return Container(
          width: 56,
          height: 56,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            color: CupertinoColors.systemGrey6,
          ),
          child: Center(
            child: Icon(
              _getDocumentIcon(file.mimeType),
              color: widget.accentColor,
              size: 28,
            ),
          ),
        );
      case FileListType.voice:
        return Container(
          width: 56,
          height: 56,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            color: CupertinoColors.systemGrey6,
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                CupertinoIcons.mic_fill,
                color: widget.accentColor,
                size: 24,
              ),
              const SizedBox(height: 4),
              Text(
                file.metadata?['duration'] ?? '0:00',
                style: TextStyle(
                  color: widget.accentColor,
                  fontSize: 10,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        );
    }
  }

  IconData _getDocumentIcon(String mimeType) {
    if (mimeType.contains('pdf')) {
      return CupertinoIcons.doc_text_fill;
    } else if (mimeType.contains('word') || mimeType.contains('doc')) {
      return CupertinoIcons.doc_fill;
    } else if (mimeType.contains('spreadsheet') ||
        mimeType.contains('excel') ||
        mimeType.contains('xls')) {
      return CupertinoIcons.table_fill;
    } else if (mimeType.contains('text/plain')) {
      return CupertinoIcons.doc_text;
    } else {
      return CupertinoIcons.doc;
    }
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays == 0) {
      return 'Today';
    } else if (difference.inDays == 1) {
      return 'Yesterday';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} days ago';
    } else if (difference.inDays < 30) {
      final weeks = (difference.inDays / 7).floor();
      return '$weeks ${weeks == 1 ? 'week' : 'weeks'} ago';
    } else if (difference.inDays < 365) {
      final months = (difference.inDays / 30).floor();
      return '$months ${months == 1 ? 'month' : 'months'} ago';
    } else {
      final years = (difference.inDays / 365).floor();
      return '$years ${years == 1 ? 'year' : 'years'} ago';
    }
  }
}
