// Copyright 2023, the hate<PERSON>ragab project author.
// All rights reserved. Use of this source code is governed by a
// MIT license that can be found in the LICENSE file.

import 'dart:math';
import 'package:flutter/cupertino.dart';
import '../controllers/storage_controller.dart';
import '../views/file_list_page.dart';
import '../views/premium_upgrade_page.dart';
import '../views/file_reminders_page.dart';

class StorageUsageOverviewPage extends StatelessWidget {
  final StorageController controller;

  const StorageUsageOverviewPage({
    super.key,
    required this.controller,
  });

  @override
  Widget build(BuildContext context) {
    return CupertinoPageScaffold(
      child: NestedScrollView(
        headerSliverBuilder: (context, innerBoxIsScrolled) => [
          const CupertinoSliverNavigationBar(
            largeTitle: Text('Storage Management'),
          )
        ],
        body: SafeArea(
          top: false,
          child: AnimatedBuilder(
            animation: controller,
            builder: (context, child) {
              if (controller.isLoading) {
                return const Center(
                  child: CupertinoActivityIndicator(),
                );
              }

              if (controller.hasError) {
                return Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(
                        CupertinoIcons.exclamationmark_circle,
                        size: 48,
                        color: CupertinoColors.systemRed,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        controller.errorMessage,
                        style: const TextStyle(
                          fontSize: 16,
                          color: CupertinoColors.systemGrey,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 24),
                      CupertinoButton(
                        onPressed: controller.fetchStorageData,
                        child: const Text('Retry'),
                      ),
                    ],
                  ),
                );
              }

              final storageData = controller.storageData;
              final usagePercentage = storageData.usagePercentage;

              Color progressColor = CupertinoColors.activeBlue;
              if (usagePercentage >= 90) {
                progressColor = CupertinoColors.systemRed;
              } else if (usagePercentage >= 70) {
                progressColor = CupertinoColors.systemOrange;
              }

              return ListView(
                padding: const EdgeInsets.all(16),
                children: [
                  // Storage usage overview card
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: CupertinoColors.systemBackground,
                      borderRadius: BorderRadius.circular(12),
                      boxShadow: [
                        BoxShadow(
                          color:
                              CupertinoColors.systemGrey.withValues(alpha: 0.1),
                          blurRadius: 10,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            const Text(
                              'Storage Usage',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                            Text(
                              '${usagePercentage.toStringAsFixed(1)}%',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.w600,
                                color: progressColor,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 12),
                        // Progress bar
                        Container(
                          height: 8,
                          width: double.infinity,
                          decoration: BoxDecoration(
                            color: CupertinoColors.systemGrey5,
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: FractionallySizedBox(
                            widthFactor: usagePercentage / 100,
                            child: Container(
                              decoration: BoxDecoration(
                                color: progressColor,
                                borderRadius: BorderRadius.circular(4),
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(height: 12),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              '${controller.formatBytes(storageData.totalUsage)} used',
                              style: const TextStyle(
                                fontSize: 14,
                                color: CupertinoColors.systemGrey,
                              ),
                            ),
                            Text(
                              '${controller.formatBytes(storageData.remainingStorage)} free of ${controller.formatBytes(storageData.totalLimit)}',
                              style: const TextStyle(
                                fontSize: 14,
                                color: CupertinoColors.systemGrey,
                              ),
                            ),
                          ],
                        ),
                        if (storageData.isStorageWarning) ...[
                          const SizedBox(height: 16),
                          Container(
                            padding: const EdgeInsets.all(12),
                            decoration: BoxDecoration(
                              color: storageData.isStorageCritical
                                  ? CupertinoColors.systemRed
                                      .withValues(alpha: 0.1)
                                  : CupertinoColors.systemOrange
                                      .withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Row(
                              children: [
                                Icon(
                                  CupertinoIcons.exclamationmark_triangle,
                                  color: storageData.isStorageCritical
                                      ? CupertinoColors.systemRed
                                      : CupertinoColors.systemOrange,
                                  size: 20,
                                ),
                                const SizedBox(width: 8),
                                Expanded(
                                  child: Text(
                                    storageData.isStorageCritical
                                        ? 'Your storage is almost full. Delete files or upgrade to premium.'
                                        : 'Your storage is getting full. Consider deleting unused files.',
                                    style: TextStyle(
                                      fontSize: 14,
                                      color: storageData.isStorageCritical
                                          ? CupertinoColors.systemRed
                                          : CupertinoColors.systemOrange,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),

                  const SizedBox(height: 24),

                  // Media type breakdown section
                  const Text(
                    'Media Types',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 16),

                  // Images
                  _buildMediaTypeCard(
                    context,
                    icon: CupertinoIcons.photo,
                    title: 'Images',
                    fileCount: storageData.imageCount,
                    fileSize: controller.formatBytes(storageData.imageUsage),
                    percentage:
                        (storageData.imageUsage / storageData.totalUsage) * 100,
                    color: CupertinoColors.activeBlue,
                    onTap: () {
                      Navigator.of(context).push(
                        CupertinoPageRoute(
                          builder: (context) => FileListPage(
                            type: FileListType.images,
                            title: 'Images',
                            accentColor: CupertinoColors.activeBlue,
                            icon: CupertinoIcons.photo,
                            controller: controller,
                          ),
                        ),
                      );
                    },
                  ),

                  const SizedBox(height: 12),

                  // Videos
                  _buildMediaTypeCard(
                    context,
                    icon: CupertinoIcons.video_camera,
                    title: 'Videos',
                    fileCount: storageData.videoCount,
                    fileSize: controller.formatBytes(storageData.videoUsage),
                    percentage:
                        (storageData.videoUsage / storageData.totalUsage) * 100,
                    color: CupertinoColors.activeGreen,
                    onTap: () {
                      Navigator.of(context).push(
                        CupertinoPageRoute(
                          builder: (context) => FileListPage(
                            type: FileListType.videos,
                            title: 'Videos',
                            accentColor: CupertinoColors.activeGreen,
                            icon: CupertinoIcons.video_camera,
                            controller: controller,
                          ),
                        ),
                      );
                    },
                  ),

                  const SizedBox(height: 12),

                  // Documents
                  _buildMediaTypeCard(
                    context,
                    icon: CupertinoIcons.doc,
                    title: 'Documents',
                    fileCount: storageData.documentCount,
                    fileSize: controller.formatBytes(storageData.documentUsage),
                    percentage:
                        (storageData.documentUsage / storageData.totalUsage) *
                            100,
                    color: CupertinoColors.systemOrange,
                    onTap: () {
                      Navigator.of(context).push(
                        CupertinoPageRoute(
                          builder: (context) => FileListPage(
                            type: FileListType.documents,
                            title: 'Documents',
                            accentColor: CupertinoColors.systemOrange,
                            icon: CupertinoIcons.doc,
                            controller: controller,
                          ),
                        ),
                      );
                    },
                  ),

                  const SizedBox(height: 12),

                  // Voice messages
                  _buildMediaTypeCard(
                    context,
                    icon: CupertinoIcons.mic,
                    title: 'Voice Messages',
                    fileCount: storageData.voiceCount,
                    fileSize: controller.formatBytes(storageData.voiceUsage),
                    percentage:
                        (storageData.voiceUsage / storageData.totalUsage) * 100,
                    color: CupertinoColors.systemPurple,
                    onTap: () {
                      Navigator.of(context).push(
                        CupertinoPageRoute(
                          builder: (context) => FileListPage(
                            type: FileListType.voice,
                            title: 'Voice Messages',
                            accentColor: CupertinoColors.systemPurple,
                            icon: CupertinoIcons.mic,
                            controller: controller,
                          ),
                        ),
                      );
                    },
                  ),

                  const SizedBox(height: 24),

                  // Premium upgrade button
                  CupertinoButton.filled(
                    onPressed: () {
                      Navigator.of(context).push(
                        CupertinoPageRoute(
                          builder: (context) => const PremiumUpgradePage(),
                        ),
                      );
                    },
                    child: const Text('Upgrade to Premium'),
                  ),

                  const SizedBox(height: 12),

                  // File reminders button
                  CupertinoButton(
                    onPressed: () {
                      Navigator.of(context).push(
                        CupertinoPageRoute(
                          builder: (context) => FileRemindersPage(
                            controller: controller,
                          ),
                        ),
                      );
                    },
                    child: const Text('Set File Reminders'),
                  ),
                ],
              );
            },
          ),
        ),
      ),
    );
  }

  Widget _buildMediaTypeCard(
    BuildContext context, {
    required IconData icon,
    required String title,
    required int fileCount,
    required String fileSize,
    required double percentage,
    required Color color,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: CupertinoColors.systemBackground,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: CupertinoColors.systemGrey.withValues(alpha: 0.1),
              blurRadius: 5,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          children: [
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                icon,
                color: color,
                size: 24,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '$fileCount files · $fileSize',
                    style: const TextStyle(
                      fontSize: 14,
                      color: CupertinoColors.systemGrey,
                    ),
                  ),
                ],
              ),
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  '${percentage.toStringAsFixed(1)}%',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: color,
                  ),
                ),
                const SizedBox(height: 4),
                const Icon(
                  CupertinoIcons.chevron_right,
                  color: CupertinoColors.systemGrey,
                  size: 18,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
