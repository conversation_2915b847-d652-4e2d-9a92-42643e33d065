// Copyright 2023, the hate<PERSON><PERSON>b project author.
// All rights reserved. Use of this source code is governed by a
// MIT license that can be found in the LICENSE file.

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import '../controllers/storage_controller.dart';
import '../models/file_metadata.dart';

class FileRemindersPage extends StatefulWidget {
  final StorageController controller;

  const FileRemindersPage({
    super.key,
    required this.controller,
  });

  @override
  State<FileRemindersPage> createState() => _FileRemindersPageState();
}

class _FileRemindersPageState extends State<FileRemindersPage> {
  bool isLoading = false;
  List<FileReminderItem> reminders = <FileReminderItem>[];

  @override
  void initState() {
    super.initState();
    _loadReminders();
  }

  Future<void> _loadReminders() async {
    try {
      setState(() {
        isLoading = true;
      });

      // TODO: Replace with actual API call to fetch reminders
      await Future.delayed(const Duration(seconds: 1));

      // Mock data for now
      final mockReminders = [
        FileReminderItem(
          id: '1',
          file: widget.controller.imageFiles.isNotEmpty
              ? widget.controller.imageFiles[0]
              : FileMetadata(
                  id: 'img_1',
                  name: 'Project Screenshot.jpg',
                  path: '/storage/images/screenshot.jpg',
                  mimeType: 'image/jpeg',
                  size: 1500000,
                  createdAt: DateTime.now().subtract(const Duration(days: 5)),
                ),
          reminderDate: DateTime.now().add(const Duration(days: 3)),
          note: 'Review project screenshot before meeting',
        ),
        FileReminderItem(
          id: '2',
          file: FileMetadata(
            id: 'doc_1',
            name: 'Quarterly Report.pdf',
            path: '/storage/documents/report.pdf',
            mimeType: 'application/pdf',
            size: 3500000,
            createdAt: DateTime.now().subtract(const Duration(days: 10)),
          ),
          reminderDate: DateTime.now().add(const Duration(days: 7)),
          note: 'Submit quarterly report to management',
        ),
        FileReminderItem(
          id: '3',
          file: FileMetadata(
            id: 'vid_1',
            name: 'Product Demo.mp4',
            path: '/storage/videos/demo.mp4',
            mimeType: 'video/mp4',
            size: 25000000,
            createdAt: DateTime.now().subtract(const Duration(days: 2)),
          ),
          reminderDate: DateTime.now().add(const Duration(days: 1)),
          note: 'Share product demo with the team',
        ),
      ];

      setState(() {
        reminders = mockReminders;
      });
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Failed to load reminders. Please try again.'),
          ),
        );
      }
    } finally {
      setState(() {
        isLoading = false;
      });
    }
  }

  Future<void> _deleteReminder(String id) async {
    try {
      // TODO: Replace with actual API call to delete reminder
      await Future.delayed(const Duration(seconds: 1));

      // Remove from local list
      setState(() {
        reminders.removeWhere((reminder) => reminder.id == id);
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Reminder deleted successfully'),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Failed to delete reminder. Please try again.'),
          ),
        );
      }
    }
  }

  void _showAddReminderDialog() {
    FileMetadata? selectedFile;
    DateTime selectedDate = DateTime.now().add(const Duration(days: 1));
    final noteController = TextEditingController();

    showCupertinoModalPopup(
      context: context,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.7,
        padding: const EdgeInsets.all(16),
        decoration: const BoxDecoration(
          color: CupertinoColors.systemBackground,
          borderRadius: BorderRadius.vertical(
            top: Radius.circular(20),
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Center(
              child: Text(
                'Add Reminder',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
            const SizedBox(height: 24),

            const Text(
              'Select File',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),

            // File selection dropdown (simplified for demo)
            CupertinoButton(
              padding: EdgeInsets.zero,
              child: Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
                decoration: BoxDecoration(
                  border: Border.all(
                    color: CupertinoColors.systemGrey4,
                  ),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      selectedFile?.name ?? 'Select a file',
                      style: TextStyle(
                        color: selectedFile != null
                            ? CupertinoColors.label
                            : CupertinoColors.systemGrey,
                      ),
                    ),
                    const Icon(
                      CupertinoIcons.chevron_down,
                      size: 16,
                      color: CupertinoColors.systemGrey,
                    ),
                  ],
                ),
              ),
              onPressed: () {
                // In a real app, show a file picker here
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text(
                        'File selection functionality will be implemented soon'),
                  ),
                );

                // For demo purposes, just set a mock file
                setState(() {
                  selectedFile = FileMetadata(
                    id: 'doc_new',
                    name: 'Selected Document.pdf',
                    path: '/storage/documents/selected.pdf',
                    mimeType: 'application/pdf',
                    size: 2500000,
                    createdAt: DateTime.now(),
                  );
                });
              },
            ),

            const SizedBox(height: 16),

            const Text(
              'Reminder Date',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),

            // Date picker
            SizedBox(
              height: 150,
              child: CupertinoDatePicker(
                mode: CupertinoDatePickerMode.dateAndTime,
                initialDateTime: selectedDate,
                minimumDate: DateTime.now(),
                maximumDate: DateTime.now().add(const Duration(days: 365)),
                onDateTimeChanged: (date) {
                  selectedDate = date;
                },
              ),
            ),

            const SizedBox(height: 16),

            const Text(
              'Note',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),

            // Note input
            CupertinoTextField(
              controller: noteController,
              placeholder: 'Add a note for this reminder',
              padding: const EdgeInsets.all(12),
              maxLines: 3,
              decoration: BoxDecoration(
                border: Border.all(
                  color: CupertinoColors.systemGrey4,
                ),
                borderRadius: BorderRadius.circular(8),
              ),
            ),

            const Spacer(),

            // Action buttons
            Row(
              children: [
                Expanded(
                  child: CupertinoButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: const Text('Cancel'),
                  ),
                ),
                Expanded(
                  child: CupertinoButton.filled(
                    onPressed: () {
                      if (selectedFile == null) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text('Please select a file'),
                          ),
                        );
                        return;
                      }

                      if (noteController.text.trim().isEmpty) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text('Please add a note'),
                          ),
                        );
                        return;
                      }

                      // Add new reminder
                      final newReminder = FileReminderItem(
                        id: DateTime.now().millisecondsSinceEpoch.toString(),
                        file: selectedFile!,
                        reminderDate: selectedDate,
                        note: noteController.text.trim(),
                      );

                      setState(() {
                        reminders.add(newReminder);
                      });
                      Navigator.of(context).pop();

                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('Reminder added successfully'),
                        ),
                      );
                    },
                    child: const Text('Add Reminder'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return CupertinoPageScaffold(
      navigationBar: CupertinoNavigationBar(
        middle: const Text('File Reminders'),
        trailing: CupertinoButton(
          padding: EdgeInsets.zero,
          onPressed: _showAddReminderDialog,
          child: const Icon(
            CupertinoIcons.add,
            size: 24,
          ),
        ),
      ),
      child: SafeArea(
        child: isLoading
            ? const Center(
                child: CupertinoActivityIndicator(),
              )
            : reminders.isEmpty
                ? Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(
                          CupertinoIcons.bell_slash,
                          size: 64,
                          color: CupertinoColors.systemGrey,
                        ),
                        const SizedBox(height: 16),
                        const Text(
                          'No reminders set',
                          style: TextStyle(
                            fontSize: 18,
                            color: CupertinoColors.systemGrey,
                          ),
                        ),
                        const SizedBox(height: 8),
                        const Text(
                          'Add reminders to get notified about your files',
                          style: TextStyle(
                            fontSize: 14,
                            color: CupertinoColors.systemGrey,
                          ),
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 24),
                        CupertinoButton.filled(
                          onPressed: _showAddReminderDialog,
                          child: const Text('Add Reminder'),
                        ),
                      ],
                    ),
                  )
                : ListView.builder(
                    padding: const EdgeInsets.all(16),
                    itemCount: reminders.length,
                    itemBuilder: (context, index) {
                      final reminder = reminders[index];
                      return _buildReminderItem(reminder);
                    },
                  ),
      ),
    );
  }

  Widget _buildReminderItem(FileReminderItem reminder) {
    final daysLeft = reminder.reminderDate.difference(DateTime.now()).inDays;
    final hoursLeft =
        reminder.reminderDate.difference(DateTime.now()).inHours % 24;

    String timeLeftText;
    Color timeLeftColor;

    if (daysLeft > 0) {
      timeLeftText = '$daysLeft ${daysLeft == 1 ? 'day' : 'days'} left';
      timeLeftColor = CupertinoColors.systemBlue;
    } else if (hoursLeft > 0) {
      timeLeftText = '$hoursLeft ${hoursLeft == 1 ? 'hour' : 'hours'} left';
      timeLeftColor = CupertinoColors.systemOrange;
    } else {
      timeLeftText = 'Due now';
      timeLeftColor = CupertinoColors.systemRed;
    }

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: CupertinoColors.systemBackground,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: CupertinoColors.systemGrey.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // File info section
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                _buildFileIcon(reminder.file),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        reminder.file.name,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 4),
                      Text(
                        widget.controller.formatBytes(reminder.file.size),
                        style: const TextStyle(
                          fontSize: 14,
                          color: CupertinoColors.systemGrey,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          Container(
            height: 1,
            color: CupertinoColors.separator,
          ),

          // Reminder info section
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Row(
                      children: [
                        const Icon(
                          CupertinoIcons.bell_fill,
                          size: 16,
                          color: CupertinoColors.systemBlue,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          _formatDate(reminder.reminderDate),
                          style: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: timeLeftColor.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Text(
                        timeLeftText,
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                          color: timeLeftColor,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                Text(
                  reminder.note,
                  style: const TextStyle(
                    fontSize: 14,
                  ),
                ),
                const SizedBox(height: 16),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    CupertinoButton(
                      padding: EdgeInsets.zero,
                      child: const Row(
                        children: [
                          Icon(
                            CupertinoIcons.pencil,
                            size: 16,
                            color: CupertinoColors.systemBlue,
                          ),
                          SizedBox(width: 4),
                          Text(
                            'Edit',
                            style: TextStyle(
                              fontSize: 14,
                            ),
                          ),
                        ],
                      ),
                      onPressed: () {
                        // TODO: Implement edit functionality
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text(
                                'Edit functionality will be implemented soon'),
                          ),
                        );
                      },
                    ),
                    CupertinoButton(
                      padding: EdgeInsets.zero,
                      child: const Row(
                        children: [
                          Icon(
                            CupertinoIcons.delete,
                            size: 16,
                            color: CupertinoColors.destructiveRed,
                          ),
                          SizedBox(width: 4),
                          Text(
                            'Delete',
                            style: TextStyle(
                              fontSize: 14,
                              color: CupertinoColors.destructiveRed,
                            ),
                          ),
                        ],
                      ),
                      onPressed: () => _deleteReminder(reminder.id),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFileIcon(FileMetadata file) {
    if (file.isImage) {
      return Container(
        width: 48,
        height: 48,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          color: CupertinoColors.systemGrey6,
        ),
        child: const Icon(
          CupertinoIcons.photo,
          color: CupertinoColors.activeBlue,
          size: 24,
        ),
      );
    } else if (file.isVideo) {
      return Container(
        width: 48,
        height: 48,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          color: CupertinoColors.systemGrey6,
        ),
        child: const Icon(
          CupertinoIcons.video_camera,
          color: CupertinoColors.activeGreen,
          size: 24,
        ),
      );
    } else if (file.isAudio) {
      return Container(
        width: 48,
        height: 48,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          color: CupertinoColors.systemGrey6,
        ),
        child: const Icon(
          CupertinoIcons.mic,
          color: CupertinoColors.systemPurple,
          size: 24,
        ),
      );
    } else {
      return Container(
        width: 48,
        height: 48,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          color: CupertinoColors.systemGrey6,
        ),
        child: const Icon(
          CupertinoIcons.doc,
          color: CupertinoColors.systemOrange,
          size: 24,
        ),
      );
    }
  }

  String _formatDate(DateTime date) {
    final day = date.day.toString().padLeft(2, '0');
    final month = date.month.toString().padLeft(2, '0');
    final year = date.year;
    final hour = date.hour.toString().padLeft(2, '0');
    final minute = date.minute.toString().padLeft(2, '0');

    return '$day/$month/$year $hour:$minute';
  }
}

class FileReminderItem {
  final String id;
  final FileMetadata file;
  final DateTime reminderDate;
  final String note;

  FileReminderItem({
    required this.id,
    required this.file,
    required this.reminderDate,
    required this.note,
  });
}
