// Copyright 2023, the hatemragab project author.
// All rights reserved. Use of this source code is governed by a
// MIT license that can be found in the LICENSE file.

import 'package:flutter/cupertino.dart';

class PremiumUpgradePage extends StatelessWidget {
  const PremiumUpgradePage({super.key});

  @override
  Widget build(BuildContext context) {
    return CupertinoPageScaffold(
      navigationBar: CupertinoNavigationBar(
        middle: const Text('Premium Storage'),
        leading: CupertinoNavigationBarBackButton(
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      child: SafeArea(
        child: Center(
          child: Padding(
            padding: const EdgeInsets.all(24.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Premium icon
                Container(
                  width: 120,
                  height: 120,
                  decoration: const BoxDecoration(
                    shape: BoxShape.circle,
                    color: CupertinoColors.systemYellow,
                  ),
                  child: const Icon(
                    CupertinoIcons.star_fill,
                    size: 70,
                    color: CupertinoColors.white,
                  ),
                ),
                
                const SizedBox(height: 32),
                
                // Coming soon title
                const Text(
                  'Premium Feature Coming Soon',
                  style: TextStyle(
                    fontSize: 28,
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),
                
                const SizedBox(height: 16),
                
                // Description
                const Text(
                  'We\'re working on premium storage options that will give you more space for your files and media.',
                  style: TextStyle(
                    fontSize: 16,
                    color: CupertinoColors.systemGrey,
                  ),
                  textAlign: TextAlign.center,
                ),
                
                const SizedBox(height: 32),
                
                // Future features preview
                Container(
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    color: CupertinoColors.systemGrey6,
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Column(
                    children: [
                      const Text(
                        'What\'s Coming:',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(height: 16),
                      _buildFeaturePreview(
                        icon: CupertinoIcons.cloud_upload,
                        title: 'Increased Storage',
                        description: 'Up to 10GB of storage space',
                      ),
                      _buildFeaturePreview(
                        icon: CupertinoIcons.speedometer,
                        title: 'Priority Upload',
                        description: 'Faster file upload speeds',
                      ),
                      _buildFeaturePreview(
                        icon: CupertinoIcons.bell,
                        title: 'Advanced Features',
                        description: 'Enhanced file management tools',
                      ),
                    ],
                  ),
                ),
                
                const SizedBox(height: 32),
                
                // Placeholder button
                CupertinoButton.filled(
                  onPressed: () {
                    showCupertinoDialog(
                      context: context,
                      builder: (context) => CupertinoAlertDialog(
                        title: const Text('Coming Soon'),
                        content: const Text(
                          'Premium storage options will be available in a future update. Stay tuned!',
                        ),
                        actions: [
                          CupertinoDialogAction(
                            child: const Text('OK'),
                            onPressed: () => Navigator.of(context).pop(),
                          ),
                        ],
                      ),
                    );
                  },
                  child: const Text('Notify Me When Available'),
                ),
                
                const SizedBox(height: 16),
                
                // Back to storage button
                CupertinoButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('Back to Storage Management'),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
  
  Widget _buildFeaturePreview({
    required IconData icon,
    required String title,
    required String description,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: const BoxDecoration(
              shape: BoxShape.circle,
              color: CupertinoColors.systemYellow,
            ),
            child: Icon(
              icon,
              color: CupertinoColors.white,
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  description,
                  style: const TextStyle(
                    fontSize: 12,
                    color: CupertinoColors.systemGrey,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}