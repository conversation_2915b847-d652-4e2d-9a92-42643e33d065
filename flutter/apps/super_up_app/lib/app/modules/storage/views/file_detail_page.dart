// Copyright 2023, the hate<PERSON><PERSON>b project author.
// All rights reserved. Use of this source code is governed by a
// MIT license that can be found in the LICENSE file.

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import '../controllers/storage_controller.dart';
import '../models/file_metadata.dart';

class FileDetailPage extends StatelessWidget {
  final FileMetadata file;
  final StorageController controller;

  const FileDetailPage({
    super.key,
    required this.file,
    required this.controller,
  });

  @override
  Widget build(BuildContext context) {
    return CupertinoPageScaffold(
      navigationBar: CupertinoNavigationBar(
        middle: Text(file.name),
        trailing: CupertinoButton(
          padding: EdgeInsets.zero,
          child: const Icon(
            CupertinoIcons.share,
            size: 22,
          ),
          onPressed: () {
            // TODO: Implement file sharing
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Sharing functionality will be implemented soon'),
              ),
            );
          },
        ),
      ),
      child: <PERSON><PERSON><PERSON>(
        child: <PERSON><PERSON><PERSON>w(
          padding: const EdgeInsets.all(16),
          children: [
            // File preview
            _buildFilePreview(),

            const SizedBox(height: 24),

            // File information
            const Text(
              'File Information',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 16),

            _buildInfoRow('Name', file.name),
            _buildInfoRow('Size', controller.formatBytes(file.size)),
            _buildInfoRow('Type', _getFileType()),
            _buildInfoRow('Created', _formatDate(file.createdAt)),
            if (file.lastAccessed != null)
              _buildInfoRow('Last accessed', _formatDate(file.lastAccessed!)),

            // Additional metadata
            if (file.metadata != null && file.metadata!.isNotEmpty) ...[
              const SizedBox(height: 24),
              const Text(
                'Additional Information',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 16),
              ...file.metadata!.entries.map((entry) => _buildInfoRow(
                  _capitalizeFirstLetter(entry.key), entry.value.toString())),
            ],

            const SizedBox(height: 32),

            // Action buttons
            CupertinoButton.filled(
              onPressed: () {
                // TODO: Implement file download
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content:
                        Text('Download functionality will be implemented soon'),
                  ),
                );
              },
              child: const Text('Download'),
            ),

            const SizedBox(height: 12),

            CupertinoButton(
              onPressed: () async {
                // Show confirmation dialog
                final shouldDelete = await showCupertinoDialog<bool>(
                  context: context,
                  builder: (context) => CupertinoAlertDialog(
                    title: const Text('Delete File'),
                    content: Text(
                        'Are you sure you want to delete "${file.name}"? This action cannot be undone.'),
                    actions: [
                      CupertinoDialogAction(
                        isDestructiveAction: true,
                        onPressed: () => Navigator.of(context).pop(true),
                        child: const Text('Delete'),
                      ),
                      CupertinoDialogAction(
                        isDefaultAction: true,
                        onPressed: () => Navigator.of(context).pop(false),
                        child: const Text('Cancel'),
                      ),
                    ],
                  ),
                );

                if (shouldDelete == true) {
                  final success = await controller.deleteFile(file);
                  if (success) {
                    Navigator.of(context).pop(); // Return to file list
                  }
                }
              },
              child: const Text(
                'Delete File',
                style: TextStyle(
                  color: CupertinoColors.destructiveRed,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFilePreview() {
    if (file.isImage) {
      return Container(
        height: 250,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          color: CupertinoColors.systemGrey6,
        ),
        child: file.url != null
            ? ClipRRect(
                borderRadius: BorderRadius.circular(12),
                child: Image.network(
                  file.url!,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) => const Center(
                    child: Icon(
                      CupertinoIcons.photo,
                      size: 64,
                      color: CupertinoColors.systemGrey,
                    ),
                  ),
                ),
              )
            : const Center(
                child: Icon(
                  CupertinoIcons.photo,
                  size: 64,
                  color: CupertinoColors.systemGrey,
                ),
              ),
      );
    } else if (file.isVideo) {
      return Container(
        height: 200,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          color: CupertinoColors.systemGrey6,
        ),
        child: Stack(
          alignment: Alignment.center,
          children: [
            if (file.thumbnailUrl != null)
              ClipRRect(
                borderRadius: BorderRadius.circular(12),
                child: Image.network(
                  file.thumbnailUrl!,
                  width: double.infinity,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) =>
                      const SizedBox(),
                ),
              ),
            Container(
              width: 64,
              height: 64,
              decoration: BoxDecoration(
                color: CupertinoColors.black.withValues(alpha: 0.5),
                shape: BoxShape.circle,
              ),
              child: const Icon(
                CupertinoIcons.play_fill,
                color: CupertinoColors.white,
                size: 32,
              ),
            ),
          ],
        ),
      );
    } else if (file.isAudio) {
      return Container(
        height: 150,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          color: CupertinoColors.systemGrey6,
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              CupertinoIcons.waveform,
              size: 64,
              color: CupertinoColors.systemPurple,
            ),
            const SizedBox(height: 16),
            Text(
              file.metadata?['duration'] ?? '0:00',
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w500,
                color: CupertinoColors.systemPurple,
              ),
            ),
          ],
        ),
      );
    } else {
      // Document preview
      return Container(
        height: 150,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          color: CupertinoColors.systemGrey6,
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              _getDocumentIcon(),
              size: 64,
              color: _getDocumentColor(),
            ),
            const SizedBox(height: 16),
            Text(
              file.name.split('.').last.toUpperCase(),
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w500,
                color: _getDocumentColor(),
              ),
            ),
          ],
        ),
      );
    }
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: const TextStyle(
                fontSize: 16,
                color: CupertinoColors.systemGrey,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                fontSize: 16,
              ),
            ),
          ),
        ],
      ),
    );
  }

  IconData _getDocumentIcon() {
    if (file.mimeType.contains('pdf')) {
      return CupertinoIcons.doc_text_fill;
    } else if (file.mimeType.contains('word') ||
        file.mimeType.contains('doc')) {
      return CupertinoIcons.doc_fill;
    } else if (file.mimeType.contains('spreadsheet') ||
        file.mimeType.contains('excel') ||
        file.mimeType.contains('xls')) {
      return CupertinoIcons.table_fill;
    } else if (file.mimeType.contains('text/plain')) {
      return CupertinoIcons.doc_text;
    } else {
      return CupertinoIcons.doc;
    }
  }

  Color _getDocumentColor() {
    if (file.mimeType.contains('pdf')) {
      return CupertinoColors.systemRed;
    } else if (file.mimeType.contains('word') ||
        file.mimeType.contains('doc')) {
      return CupertinoColors.systemBlue;
    } else if (file.mimeType.contains('spreadsheet') ||
        file.mimeType.contains('excel') ||
        file.mimeType.contains('xls')) {
      return CupertinoColors.systemGreen;
    } else {
      return CupertinoColors.systemOrange;
    }
  }

  String _getFileType() {
    if (file.isImage) {
      return 'Image';
    } else if (file.isVideo) {
      return 'Video';
    } else if (file.isAudio) {
      return 'Audio';
    } else {
      if (file.mimeType.contains('pdf')) {
        return 'PDF Document';
      } else if (file.mimeType.contains('word') ||
          file.mimeType.contains('doc')) {
        return 'Word Document';
      } else if (file.mimeType.contains('spreadsheet') ||
          file.mimeType.contains('excel') ||
          file.mimeType.contains('xls')) {
        return 'Spreadsheet';
      } else if (file.mimeType.contains('text/plain')) {
        return 'Text Document';
      } else {
        return 'Document';
      }
    }
  }

  String _formatDate(DateTime date) {
    final day = date.day.toString().padLeft(2, '0');
    final month = date.month.toString().padLeft(2, '0');
    final year = date.year;
    final hour = date.hour.toString().padLeft(2, '0');
    final minute = date.minute.toString().padLeft(2, '0');

    return '$day/$month/$year $hour:$minute';
  }

  String _capitalizeFirstLetter(String text) {
    if (text.isEmpty) return text;
    return text[0].toUpperCase() + text.substring(1);
  }
}
