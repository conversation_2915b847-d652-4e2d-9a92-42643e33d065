// Copyright 2023, the hate<PERSON>ragab project author.
// All rights reserved. Use of this source code is governed by a
// MIT license that can be found in the LICENSE file.

import 'dart:math' as math;
import 'package:flutter/foundation.dart';
import '../models/file_metadata.dart';

class StorageUsageData {
  final double totalUsage; // in bytes
  final double totalLimit; // in bytes
  final double imageUsage; // in bytes
  final double videoUsage; // in bytes
  final double documentUsage; // in bytes
  final double voiceUsage; // in bytes
  final int imageCount;
  final int videoCount;
  final int documentCount;
  final int voiceCount;

  StorageUsageData({
    required this.totalUsage,
    required this.totalLimit,
    required this.imageUsage,
    required this.videoUsage,
    required this.documentUsage,
    required this.voiceUsage,
    required this.imageCount,
    required this.videoCount,
    required this.documentCount,
    required this.voiceCount,
  });

  double get usagePercentage => (totalUsage / totalLimit) * 100;
  
  double get remainingStorage => totalLimit - totalUsage;
  
  bool get isStorageFull => totalUsage >= totalLimit;
  
  bool get isStorageWarning => usagePercentage >= 70;
  
  bool get isStorageCritical => usagePercentage >= 90;
}

class StorageController extends ChangeNotifier {
  StorageUsageData _storageData = StorageUsageData(
    totalUsage: 0,
    totalLimit: 1024 * 1024 * 1024, // 1GB in bytes
    imageUsage: 0,
    videoUsage: 0,
    documentUsage: 0,
    voiceUsage: 0,
    imageCount: 0,
    videoCount: 0,
    documentCount: 0,
    voiceCount: 0,
  );

  bool _isLoading = true;
  bool _hasError = false;
  String _errorMessage = '';
  
  // Files by category
  List<FileMetadata> _imageFiles = [];
  List<FileMetadata> _videoFiles = [];
  List<FileMetadata> _documentFiles = [];
  List<FileMetadata> _voiceFiles = [];
  
  // Category-specific loading states
  bool _isLoadingImages = false;
  bool _isLoadingVideos = false;
  bool _isLoadingDocuments = false;
  bool _isLoadingVoice = false;

  // Getters
  StorageUsageData get storageData => _storageData;
  bool get isLoading => _isLoading;
  bool get hasError => _hasError;
  String get errorMessage => _errorMessage;
  List<FileMetadata> get imageFiles => _imageFiles;
  List<FileMetadata> get videoFiles => _videoFiles;
  List<FileMetadata> get documentFiles => _documentFiles;
  List<FileMetadata> get voiceFiles => _voiceFiles;
  bool get isLoadingImages => _isLoadingImages;
  bool get isLoadingVideos => _isLoadingVideos;
  bool get isLoadingDocuments => _isLoadingDocuments;
  bool get isLoadingVoice => _isLoadingVoice;

  StorageController() {
    fetchStorageData();
  }

  Future<void> fetchStorageData() async {
    try {
      _isLoading = true;
      _hasError = false;
      notifyListeners();
      
      // TODO: Replace with actual API call to fetch storage data
      await Future.delayed(const Duration(seconds: 1));
      
      // Mock data for now
      _storageData = StorageUsageData(
        totalUsage: 650 * 1024 * 1024, // 650MB
        totalLimit: 1024 * 1024 * 1024, // 1GB
        imageUsage: 300 * 1024 * 1024, // 300MB
        videoUsage: 250 * 1024 * 1024, // 250MB
        documentUsage: 50 * 1024 * 1024, // 50MB
        voiceUsage: 50 * 1024 * 1024, // 50MB
        imageCount: 120,
        videoCount: 25,
        documentCount: 30,
        voiceCount: 45,
      );
    } catch (e) {
      _hasError = true;
      _errorMessage = 'Failed to load storage data. Please try again.';
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> fetchImageFiles() async {
    try {
      _isLoadingImages = true;
      notifyListeners();
      
      // TODO: Replace with actual API call to fetch image files
      await Future.delayed(const Duration(seconds: 1));
      
      // Mock data for now
      final List<FileMetadata> mockImages = List.generate(
        _storageData.imageCount,
        (index) => FileMetadata(
          id: 'img_$index',
          name: 'Image ${index + 1}.jpg',
          path: '/storage/images/image_${index + 1}.jpg',
          mimeType: index % 2 == 0 ? 'image/jpeg' : 'image/png',
          size: (50000 + (math.Random().nextDouble() * 5000000)),
          createdAt: DateTime.now().subtract(Duration(days: math.Random().nextInt(30))),
          lastAccessed: DateTime.now().subtract(Duration(days: math.Random().nextInt(10))),
          thumbnailUrl: 'https://example.com/thumbnails/image_${index + 1}.jpg',
          url: 'https://example.com/images/image_${index + 1}.jpg',
        ),
      );
      
      _imageFiles = mockImages;
    } catch (e) {
      if (kDebugMode) {
        print('Error loading image files: $e');
      }
    } finally {
      _isLoadingImages = false;
      notifyListeners();
    }
  }
  
  Future<void> fetchVideoFiles() async {
    try {
      _isLoadingVideos = true;
      notifyListeners();
      
      // TODO: Replace with actual API call to fetch video files
      await Future.delayed(const Duration(seconds: 1));
      
      // Mock data for now
      final List<FileMetadata> mockVideos = List.generate(
        _storageData.videoCount,
        (index) => FileMetadata(
          id: 'vid_$index',
          name: 'Video ${index + 1}.mp4',
          path: '/storage/videos/video_${index + 1}.mp4',
          mimeType: 'video/mp4',
          size: (2000000 + (math.Random().nextDouble() * 10000000)),
          createdAt: DateTime.now().subtract(Duration(days: math.Random().nextInt(30))),
          lastAccessed: DateTime.now().subtract(Duration(days: math.Random().nextInt(10))),
          thumbnailUrl: 'https://example.com/thumbnails/video_${index + 1}.jpg',
          url: 'https://example.com/videos/video_${index + 1}.mp4',
          metadata: {
            'duration': '${math.Random().nextInt(5) + 1}:${math.Random().nextInt(59).toString().padLeft(2, '0')}',
            'resolution': math.Random().nextBool() ? '720p' : '1080p',
          },
        ),
      );
      
      _videoFiles = mockVideos;
    } catch (e) {
      if (kDebugMode) {
        print('Error loading video files: $e');
      }
    } finally {
      _isLoadingVideos = false;
      notifyListeners();
    }
  }
  
  Future<void> fetchDocumentFiles() async {
    try {
      _isLoadingDocuments = true;
      notifyListeners();
      
      // TODO: Replace with actual API call to fetch document files
      await Future.delayed(const Duration(seconds: 1));
      
      // Mock data for now
      final List<String> docTypes = ['pdf', 'doc', 'docx', 'txt', 'xls', 'xlsx'];
      final List<String> mimeTypes = [
        'application/pdf', 
        'application/msword', 
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'text/plain',
        'application/vnd.ms-excel',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      ];
      
      final List<FileMetadata> mockDocs = List.generate(
        _storageData.documentCount,
        (index) {
          final typeIndex = index % docTypes.length;
          return FileMetadata(
            id: 'doc_$index',
            name: 'Document ${index + 1}.${docTypes[typeIndex]}',
            path: '/storage/documents/document_${index + 1}.${docTypes[typeIndex]}',
            mimeType: mimeTypes[typeIndex],
            size: (100000 + (math.Random().nextDouble() * 2000000)),
            createdAt: DateTime.now().subtract(Duration(days: math.Random().nextInt(30))),
            lastAccessed: DateTime.now().subtract(Duration(days: math.Random().nextInt(10))),
            url: 'https://example.com/documents/document_${index + 1}.${docTypes[typeIndex]}',
          );
        },
      );
      
      _documentFiles = mockDocs;
    } catch (e) {
      if (kDebugMode) {
        print('Error loading document files: $e');
      }
    } finally {
      _isLoadingDocuments = false;
      notifyListeners();
    }
  }
  
  Future<void> fetchVoiceFiles() async {
    try {
      _isLoadingVoice = true;
      notifyListeners();
      
      // TODO: Replace with actual API call to fetch voice files
      await Future.delayed(const Duration(seconds: 1));
      
      // Mock data for now
      final List<FileMetadata> mockVoice = List.generate(
        _storageData.voiceCount,
        (index) => FileMetadata(
          id: 'voice_$index',
          name: 'Voice Message ${index + 1}.m4a',
          path: '/storage/voice/voice_${index + 1}.m4a',
          mimeType: 'audio/m4a',
          size: (500000 + (math.Random().nextDouble() * 1000000)),
          createdAt: DateTime.now().subtract(Duration(days: math.Random().nextInt(30))),
          lastAccessed: DateTime.now().subtract(Duration(days: math.Random().nextInt(10))),
          url: 'https://example.com/voice/voice_${index + 1}.m4a',
          metadata: {
            'duration': '${math.Random().nextInt(2)}:${math.Random().nextInt(59).toString().padLeft(2, '0')}',
          },
        ),
      );
      
      _voiceFiles = mockVoice;
    } catch (e) {
      if (kDebugMode) {
        print('Error loading voice files: $e');
      }
    } finally {
      _isLoadingVoice = false;
      notifyListeners();
    }
  }
  
  Future<bool> deleteFile(FileMetadata file) async {
    try {
      // TODO: Replace with actual API call to delete file
      await Future.delayed(const Duration(seconds: 1));
      
      // Mock successful deletion
      // In a real implementation, we would remove the file from the server
      // and then update the local lists
      
      if (file.isImage) {
        _imageFiles.removeWhere((f) => f.id == file.id);
        _storageData = StorageUsageData(
          totalUsage: _storageData.totalUsage - file.size,
          totalLimit: _storageData.totalLimit,
          imageUsage: _storageData.imageUsage - file.size,
          videoUsage: _storageData.videoUsage,
          documentUsage: _storageData.documentUsage,
          voiceUsage: _storageData.voiceUsage,
          imageCount: _storageData.imageCount - 1,
          videoCount: _storageData.videoCount,
          documentCount: _storageData.documentCount,
          voiceCount: _storageData.voiceCount,
        );
      } else if (file.isVideo) {
        _videoFiles.removeWhere((f) => f.id == file.id);
        _storageData = StorageUsageData(
          totalUsage: _storageData.totalUsage - file.size,
          totalLimit: _storageData.totalLimit,
          imageUsage: _storageData.imageUsage,
          videoUsage: _storageData.videoUsage - file.size,
          documentUsage: _storageData.documentUsage,
          voiceUsage: _storageData.voiceUsage,
          imageCount: _storageData.imageCount,
          videoCount: _storageData.videoCount - 1,
          documentCount: _storageData.documentCount,
          voiceCount: _storageData.voiceCount,
        );
      } else if (file.isAudio) {
        _voiceFiles.removeWhere((f) => f.id == file.id);
        _storageData = StorageUsageData(
          totalUsage: _storageData.totalUsage - file.size,
          totalLimit: _storageData.totalLimit,
          imageUsage: _storageData.imageUsage,
          videoUsage: _storageData.videoUsage,
          documentUsage: _storageData.documentUsage,
          voiceUsage: _storageData.voiceUsage - file.size,
          imageCount: _storageData.imageCount,
          videoCount: _storageData.videoCount,
          documentCount: _storageData.documentCount,
          voiceCount: _storageData.voiceCount - 1,
        );
      } else {
        _documentFiles.removeWhere((f) => f.id == file.id);
        _storageData = StorageUsageData(
          totalUsage: _storageData.totalUsage - file.size,
          totalLimit: _storageData.totalLimit,
          imageUsage: _storageData.imageUsage,
          videoUsage: _storageData.videoUsage,
          documentUsage: _storageData.documentUsage - file.size,
          voiceUsage: _storageData.voiceUsage,
          imageCount: _storageData.imageCount,
          videoCount: _storageData.videoCount,
          documentCount: _storageData.documentCount - 1,
          voiceCount: _storageData.voiceCount,
        );
      }
      
      notifyListeners();
      return true;
    } catch (e) {
      if (kDebugMode) {
        print('Error deleting file: $e');
      }
      return false;
    }
  }

  /// Delete multiple files and update storage usage
  Future<bool> deleteMultipleFiles(List<FileMetadata> files) async {
    try {
      // TODO: Replace with actual API call to delete multiple files
      await Future.delayed(const Duration(seconds: 2));
      
      double totalSizeFreed = 0;
      int imagesDeleted = 0;
      int videosDeleted = 0;
      int documentsDeleted = 0;
      int voiceDeleted = 0;
      double imageSizeFreed = 0;
      double videoSizeFreed = 0;
      double documentSizeFreed = 0;
      double voiceSizeFreed = 0;
      
      // Calculate totals and remove files from local lists
      for (final file in files) {
        totalSizeFreed += file.size;
        
        if (file.isImage) {
          _imageFiles.removeWhere((f) => f.id == file.id);
          imagesDeleted++;
          imageSizeFreed += file.size;
        } else if (file.isVideo) {
          _videoFiles.removeWhere((f) => f.id == file.id);
          videosDeleted++;
          videoSizeFreed += file.size;
        } else if (file.isAudio) {
          _voiceFiles.removeWhere((f) => f.id == file.id);
          voiceDeleted++;
          voiceSizeFreed += file.size;
        } else {
          _documentFiles.removeWhere((f) => f.id == file.id);
          documentsDeleted++;
          documentSizeFreed += file.size;
        }
      }
      
      // Update storage data
      _storageData = StorageUsageData(
        totalUsage: _storageData.totalUsage - totalSizeFreed,
        totalLimit: _storageData.totalLimit,
        imageUsage: _storageData.imageUsage - imageSizeFreed,
        videoUsage: _storageData.videoUsage - videoSizeFreed,
        documentUsage: _storageData.documentUsage - documentSizeFreed,
        voiceUsage: _storageData.voiceUsage - voiceSizeFreed,
        imageCount: _storageData.imageCount - imagesDeleted,
        videoCount: _storageData.videoCount - videosDeleted,
        documentCount: _storageData.documentCount - documentsDeleted,
        voiceCount: _storageData.voiceCount - voiceDeleted,
      );
      
      notifyListeners();
      return true;
    } catch (e) {
      if (kDebugMode) {
        print('Error deleting multiple files: $e');
      }
      return false;
    }
  }

  /// Update storage usage after file deletion from backend response
  void updateStorageAfterDeletion({
    required int deletedCount,
    required double totalSizeFreed,
    required double newStorageUsage,
    required double newUsagePercentage,
  }) {
    // Force refresh storage data to get accurate counts by media type
    fetchStorageData();
    notifyListeners();
  }

  String formatBytes(double bytes, {int decimals = 1}) {
    if (bytes <= 0) return '0 B';
    const suffixes = ['B', 'KB', 'MB', 'GB', 'TB'];
    var i = (math.log(bytes) / math.log(1024)).floor();
    return '${(bytes / math.pow(1024, i)).toStringAsFixed(decimals)} ${suffixes[i]}';
  }
}