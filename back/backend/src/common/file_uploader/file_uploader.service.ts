/**
 * Copyright 2023, the hate<PERSON><PERSON>b project author.
 * All rights reserved. Use of this source code is governed by a
 * MIT license that can be found in the LICENSE file.
 */

import { Injectable, Logger } from "@nestjs/common";
import path from "path";
import root from "app-root-path";
import fs from "fs";
import { S3UploaderTypes } from "../../core/utils/enums";
import { cropProfileImage } from "../../core/utils/sharp.utils";
import { fromBuffer } from "file-type";
import { v4 as uuidv4 } from "uuid";
import { CreateS3UploaderDto } from "./create-s3_uploader.dto";
import { FileMetadataService } from "../../api/storage_management/services";
import { StorageService } from "../../api/storage_management/services/storage.service";
import { MediaType } from "../../api/storage_management/entities/file_metadata.entity";
import { StorageLimitException } from "../../api/storage_management/exceptions";

@Injectable()
export class FileUploaderService {
  private readonly logger = new Logger(FileUploaderService.name);

  constructor(
    private readonly fileMetadataService: FileMetadataService,
    private readonly storageService: StorageService
  ) {}

  async putImageCropped(imageBuffer: Buffer, myId: string) {
    try {
      // Crop the image first to get the actual size that will be stored
      let image = await cropProfileImage(imageBuffer);
      
      // Validate storage limit before uploading
      await this.storageService.validateFileUpload(myId, image.length);
      
      // Generate file key and store the image
      let key = `${S3UploaderTypes.profileImage}-${uuidv4()}.jpg`;
      await this._putFile(image, key, myId, true);
      
      return key;
    } catch (error) {
      // If it's a storage limit exception, propagate it to the client
      if (error instanceof StorageLimitException) {
        throw error;
      }
      
      this.logger.error(`Error uploading profile image: ${error.message}`, error.stack);
      throw error;
    }
  }

  async uploadChatMedia(dto: CreateS3UploaderDto) {
    try {
      // Get file type information
      let contentType = await fromBuffer(dto.mediaBuffer);
      let fileType = contentType?.mime || 'application/octet-stream';
      let extension = contentType?.ext || dto.fileName.split('.').pop() || 'bin';
      
      // Get file size
      const fileSize = dto.mediaBuffer.length;
      
      // Validate storage limit before uploading
      // This will throw StorageLimitException if the limit would be exceeded
      await this.storageService.validateFileUpload(dto.myUser._id, fileSize);
      
      // Generate file key
      let key = `${dto.myUser._id}/${S3UploaderTypes.media}-${uuidv4()}`;
      key = `${key}.${extension}`;
      
      // Save file to storage
      await this._putFile(dto.mediaBuffer, key, dto.myUser._id);
      
      // Determine media type
      const mediaType = this.determineMediaType(fileType);
      
      // Track file metadata if message context is available
      if (dto.messageId && dto.roomId) {
        await this.fileMetadataService.createFileMetadata({
          userId: dto.myUser._id,
          fileKey: key,
          fileName: dto.fileName.toString(),
          fileType: fileType,
          fileSize: fileSize,
          mediaType: mediaType,
          messageId: dto.messageId,
          roomId: dto.roomId,
          senderId: dto.myUser._id,
          receiverId: dto.receiverId
        });
      }
      
      return key;
    } catch (error) {
      // If it's a storage limit exception, propagate it to the client
      if (error instanceof StorageLimitException) {
        throw error;
      }
      
      this.logger.error(`Error uploading chat media: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Determine media type from file type
   * @param fileType MIME type of the file
   * @returns MediaType enum value
   */
  private determineMediaType(fileType: string): MediaType {
    if (fileType.startsWith('image/')) {
      return MediaType.IMAGE;
    } else if (fileType.startsWith('video/')) {
      return MediaType.VIDEO;
    } else if (fileType.startsWith('audio/')) {
      return MediaType.VOICE;
    } else {
      return MediaType.DOCUMENT;
    }
  }

  async _putFile(fileData: Buffer, key:string, userId: string, isPublic?: boolean) {
    let localPath = path.join(root.path, "public", isPublic ? "v-public" : "media", userId.toString());
    if (!fs.existsSync(localPath)) {
      fs.mkdirSync(localPath);
    }
    return await new Promise((resolve, reject) => {
      let localPath = path.join(root.path, "public", isPublic ? "v-public" : "media", key);
      fs.writeFile(localPath, fileData,  err => {
        if (err) {
          reject(err);
          console.log(err);
        }
        resolve(key);
      });
    });
  }
}
