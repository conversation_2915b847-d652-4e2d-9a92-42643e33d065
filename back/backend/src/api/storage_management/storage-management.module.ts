import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { FileMetadataSchema } from './entities/file_metadata.entity';
import { StorageMetadataSchema } from './entities/storage_metadata.entity';
import { StorageService, FileMetadataService, FileDeletionService } from './services';
import { StorageNotificationService } from './services/storage-notification.service';
import { NotificationTemplateService } from './services/notification-template.service';
import { StorageController } from './controllers/storage.controller';
import { NotificationEmitterModule } from '../../common/notification_emitter/notification_emitter.module';
import { AuthModule } from '../auth/auth.module';

/**
 * Module for storage management functionality
 * Handles user storage tracking, file metadata, and storage limits
 */
@Module({
  imports: [
    MongooseModule.forFeature([
      { name: 'file_metadata', schema: FileMetadataSchema },
      { name: 'storage_metadata', schema: StorageMetadataSchema },
    ]),
    NotificationEmitterModule,
    AuthModule,
  ],
  controllers: [StorageController],
  providers: [StorageService, FileMetadataService, FileDeletionService, StorageNotificationService, NotificationTemplateService],
  exports: [MongooseModule, StorageService, FileMetadataService, FileDeletionService, StorageNotificationService, NotificationTemplateService],
})
export class StorageManagementModule {}