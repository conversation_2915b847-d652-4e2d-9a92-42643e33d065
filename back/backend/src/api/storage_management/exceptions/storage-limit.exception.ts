import { HttpException, HttpStatus } from '@nestjs/common';
import { StorageLimitStatusDto } from '../dto/storage-usage.dto';

import { STORAGE_ERROR_MESSAGES, STORAGE_ERROR_CODES } from '../constants/storage-errors.constants';

/**
 * Custom exception for storage limit errors
 * Provides detailed information about the storage limit status
 */
export class StorageLimitException extends HttpException {
  constructor(limitStatus: StorageLimitStatusDto) {
    const response = {
      statusCode: HttpStatus.PAYLOAD_TOO_LARGE,
      message: STORAGE_ERROR_MESSAGES.STORAGE_LIMIT_REACHED,
      error: 'Storage Limit Exceeded',
      code: STORAGE_ERROR_CODES.STORAGE_LIMIT_REACHED,
      limitStatus,
    };
    super(response, HttpStatus.PAYLOAD_TOO_LARGE);
  }
}