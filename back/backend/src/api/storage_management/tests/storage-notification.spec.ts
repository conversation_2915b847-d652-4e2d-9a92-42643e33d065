import { Test, TestingModule } from '@nestjs/testing';
import { StorageNotificationService } from '../services/storage-notification.service';
import { NotificationTemplateService } from '../services/notification-template.service';
import { NotificationEmitterService } from '../../../common/notification_emitter/notification_emitter.service';
import { StorageService } from '../services/storage.service';
import { getModelToken } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { IStorageMetadata } from '../entities/storage_metadata.entity';
import { DEFAULT_STORAGE_LIMIT } from '../constants/storage-limits.constants';

describe('StorageNotificationService', () => {
  let service: StorageNotificationService;
  let notificationTemplateService: NotificationTemplateService;
  let notificationEmitter: NotificationEmitterService;
  let storageService: StorageService;
  let storageMetadataModel: Model<IStorageMetadata>;

  const mockStorageMetadataModel = {
    find: jest.fn(),
    findOne: jest.fn(),
    updateOne: jest.fn(),
    exec: jest.fn(),
  };

  const mockNotificationEmitter = {
    fcmSend: jest.fn(),
  };

  const mockStorageService = {
    calculateUserStorage: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        StorageNotificationService,
        NotificationTemplateService,
        {
          provide: NotificationEmitterService,
          useValue: mockNotificationEmitter,
        },
        {
          provide: StorageService,
          useValue: mockStorageService,
        },
        {
          provide: getModelToken('storage_metadata'),
          useValue: mockStorageMetadataModel,
        },
      ],
    }).compile();

    service = module.get<StorageNotificationService>(StorageNotificationService);
    notificationTemplateService = module.get<NotificationTemplateService>(NotificationTemplateService);
    notificationEmitter = module.get<NotificationEmitterService>(NotificationEmitterService);
    storageService = module.get<StorageService>(StorageService);
    storageMetadataModel = module.get<Model<IStorageMetadata>>(getModelToken('storage_metadata'));

    // Reset all mocks before each test
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('checkStorageThresholds', () => {
    it('should check storage thresholds for all users', async () => {
      const mockMetadata = [
        { userId: '123' },
        { userId: '456' },
      ];
      
      mockStorageMetadataModel.find.mockReturnValue({
        exec: jest.fn().mockResolvedValue(mockMetadata),
      });
      
      // Spy on the checkAndNotifyUser method
      const checkAndNotifySpy = jest.spyOn(service, 'checkAndNotifyUser').mockResolvedValue();
      
      await service.checkStorageThresholds();
      
      expect(mockStorageMetadataModel.find).toHaveBeenCalled();
      expect(checkAndNotifySpy).toHaveBeenCalledTimes(2);
      expect(checkAndNotifySpy).toHaveBeenCalledWith('123');
      expect(checkAndNotifySpy).toHaveBeenCalledWith('456');
    });
    
    it('should handle errors gracefully', async () => {
      mockStorageMetadataModel.find.mockReturnValue({
        exec: jest.fn().mockRejectedValue(new Error('Database error')),
      });
      
      const loggerSpy = jest.spyOn(service['logger'], 'error');
      
      await service.checkStorageThresholds();
      
      expect(loggerSpy).toHaveBeenCalledWith(
        'Error checking storage thresholds:',
        expect.any(Error)
      );
    });
  });

  describe('checkAndNotifyUser', () => {
    it('should send high storage notification when usage is between 70% and 89%', async () => {
      const userId = '123';
      const usagePercentage = 75;
      
      mockStorageService.calculateUserStorage.mockResolvedValue({
        totalUsage: DEFAULT_STORAGE_LIMIT * (usagePercentage / 100),
      });
      
      // Mock the shouldSendNotification method
      jest.spyOn(service as any, 'shouldSendNotification').mockResolvedValue(true);
      
      // Mock the sendStorageNotification method
      const sendNotificationSpy = jest.spyOn(service as any, 'sendStorageNotification').mockResolvedValue();
      
      // Mock the recordNotificationSent method
      const recordNotificationSpy = jest.spyOn(service as any, 'recordNotificationSent').mockResolvedValue();
      
      await service.checkAndNotifyUser(userId);
      
      expect(mockStorageService.calculateUserStorage).toHaveBeenCalledWith(userId);
      expect(sendNotificationSpy).toHaveBeenCalled();
      expect(recordNotificationSpy).toHaveBeenCalledWith(userId, 'high_storage');
    });
    
    it('should send critical storage notification when usage is between 90% and 99%', async () => {
      const userId = '123';
      const usagePercentage = 95;
      
      mockStorageService.calculateUserStorage.mockResolvedValue({
        totalUsage: DEFAULT_STORAGE_LIMIT * (usagePercentage / 100),
      });
      
      // Mock the shouldSendNotification method
      jest.spyOn(service as any, 'shouldSendNotification').mockResolvedValue(true);
      
      // Mock the sendStorageNotification method
      const sendNotificationSpy = jest.spyOn(service as any, 'sendStorageNotification').mockResolvedValue();
      
      // Mock the recordNotificationSent method
      const recordNotificationSpy = jest.spyOn(service as any, 'recordNotificationSent').mockResolvedValue();
      
      await service.checkAndNotifyUser(userId);
      
      expect(mockStorageService.calculateUserStorage).toHaveBeenCalledWith(userId);
      expect(sendNotificationSpy).toHaveBeenCalled();
      expect(recordNotificationSpy).toHaveBeenCalledWith(userId, 'critical_storage');
    });
    
    it('should send full storage notification when usage is 100% or more', async () => {
      const userId = '123';
      const usagePercentage = 100;
      
      mockStorageService.calculateUserStorage.mockResolvedValue({
        totalUsage: DEFAULT_STORAGE_LIMIT * (usagePercentage / 100),
      });
      
      // Mock the shouldSendNotification method
      jest.spyOn(service as any, 'shouldSendNotification').mockResolvedValue(true);
      
      // Mock the sendStorageNotification method
      const sendNotificationSpy = jest.spyOn(service as any, 'sendStorageNotification').mockResolvedValue();
      
      // Mock the recordNotificationSent method
      const recordNotificationSpy = jest.spyOn(service as any, 'recordNotificationSent').mockResolvedValue();
      
      await service.checkAndNotifyUser(userId);
      
      expect(mockStorageService.calculateUserStorage).toHaveBeenCalledWith(userId);
      expect(sendNotificationSpy).toHaveBeenCalled();
      expect(recordNotificationSpy).toHaveBeenCalledWith(userId, 'full_storage');
    });
    
    it('should check for storage reduction when usage is below 70%', async () => {
      const userId = '123';
      const usagePercentage = 65;
      
      mockStorageService.calculateUserStorage.mockResolvedValue({
        totalUsage: DEFAULT_STORAGE_LIMIT * (usagePercentage / 100),
      });
      
      // Mock the checkForStorageReduction method
      const checkReductionSpy = jest.spyOn(service as any, 'checkForStorageReduction').mockResolvedValue();
      
      await service.checkAndNotifyUser(userId);
      
      expect(mockStorageService.calculateUserStorage).toHaveBeenCalledWith(userId);
      expect(checkReductionSpy).toHaveBeenCalledWith(userId, usagePercentage);
    });
  });

  describe('checkForStorageReduction', () => {
    it('should send storage cleared notification when user frees up space', async () => {
      const userId = '123';
      const currentUsagePercentage = 65;
      
      // Mock storage metadata with previous high usage notification
      const mockMetadata = {
        notificationHistory: {
          high_storage: new Date(),
        },
      };
      
      mockStorageMetadataModel.findOne.mockReturnValue({
        exec: jest.fn().mockResolvedValue(mockMetadata),
      });
      
      // Mock the notification template service
      const notificationTemplate = {
        title: 'Storage Space Freed',
        body: 'Great job! You\'ve freed up space.',
        data: { level: 'normal' },
      };
      
      jest.spyOn(notificationTemplateService, 'generateStorageClearedNotification')
        .mockReturnValue(notificationTemplate);
      
      // Mock the sendStorageNotification method
      const sendNotificationSpy = jest.spyOn(service as any, 'sendStorageNotification').mockResolvedValue();
      
      await service['checkForStorageReduction'](userId, currentUsagePercentage);
      
      expect(mockStorageMetadataModel.findOne).toHaveBeenCalledWith({ userId });
      expect(notificationTemplateService.generateStorageClearedNotification).toHaveBeenCalledWith(currentUsagePercentage);
      expect(sendNotificationSpy).toHaveBeenCalledWith(
        userId,
        notificationTemplate.title,
        notificationTemplate.body,
        notificationTemplate.data
      );
      expect(mockStorageMetadataModel.updateOne).toHaveBeenCalledWith(
        { userId },
        {
          $set: {
            'notificationHistory.high_storage': null,
            'notificationHistory.critical_storage': null,
            'notificationHistory.full_storage': null,
          },
        }
      );
    });
    
    it('should not send notification if user was not previously in high usage state', async () => {
      const userId = '123';
      const currentUsagePercentage = 65;
      
      // Mock storage metadata with no previous high usage notification
      const mockMetadata = {
        notificationHistory: {},
      };
      
      mockStorageMetadataModel.findOne.mockReturnValue({
        exec: jest.fn().mockResolvedValue(mockMetadata),
      });
      
      // Mock the sendStorageNotification method
      const sendNotificationSpy = jest.spyOn(service as any, 'sendStorageNotification');
      
      await service['checkForStorageReduction'](userId, currentUsagePercentage);
      
      expect(mockStorageMetadataModel.findOne).toHaveBeenCalledWith({ userId });
      expect(sendNotificationSpy).not.toHaveBeenCalled();
      expect(mockStorageMetadataModel.updateOne).not.toHaveBeenCalled();
    });
  });
});