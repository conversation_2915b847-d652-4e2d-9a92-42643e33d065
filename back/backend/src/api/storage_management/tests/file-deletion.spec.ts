import { Test, TestingModule } from '@nestjs/testing';
import { FileDeletionService } from '../services/file-deletion.service';
import { FileMetadataService } from '../services/file-metadata.service';
import { StorageService } from '../services/storage.service';
import { NotFoundException } from '@nestjs/common';
import { MediaType } from '../entities/file_metadata.entity';
import * as fs from 'fs';

jest.mock('fs', () => ({
  existsSync: jest.fn(),
  unlinkSync: jest.fn(),
}));

describe('FileDeletionService', () => {
  let service: FileDeletionService;
  let fileMetadataService: FileMetadataService;
  let storageService: StorageService;

  const mockFileMetadataModel = {
    findOne: jest.fn(),
    find: jest.fn(),
    findByIdAndDelete: jest.fn(),
    deleteMany: jest.fn(),
  };

  const mockFileMetadataService = {
    deleteFileMetadata: jest.fn(),
    deleteMultipleFileMetadata: jest.fn(),
  };

  const mockStorageService = {
    calculateUserStorage: jest.fn(),
    updateStorageUsage: jest.fn(),
  };

  beforeEach(async () => {
    jest.clearAllMocks();

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        FileDeletionService,
        {
          provide: 'file_metadataModel',
          useValue: mockFileMetadataModel,
        },
        {
          provide: FileMetadataService,
          useValue: mockFileMetadataService,
        },
        {
          provide: StorageService,
          useValue: mockStorageService,
        },
      ],
    }).compile();

    service = module.get<FileDeletionService>(FileDeletionService);
    fileMetadataService = module.get<FileMetadataService>(FileMetadataService);
    storageService = module.get<StorageService>(StorageService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('deleteFile', () => {
    it('should delete a single file and return delete result', async () => {
      const userId = 'user123';
      const fileId = 'file123';
      const fileMetadata = {
        _id: fileId,
        userId,
        fileKey: 'media-123.jpg',
        fileName: 'test.jpg',
        fileType: 'image/jpeg',
        fileSize: 100000,
        mediaType: MediaType.IMAGE,
        messageId: 'message123',
        roomId: 'room123',
        senderId: userId,
        sentAt: new Date(),
      };

      const updatedStorage = {
        totalUsage: 900000,
        totalLimit: 1073741824, // 1GB
        usagePercentage: 0.08,
        imageUsage: 400000,
        videoUsage: 300000,
        documentUsage: 100000,
        voiceUsage: 100000,
        lastCalculatedAt: new Date(),
      };

      mockFileMetadataModel.findOne.mockResolvedValue(fileMetadata);
      (fs.existsSync as jest.Mock).mockReturnValue(true);
      mockFileMetadataService.deleteFileMetadata.mockResolvedValue(fileMetadata);
      mockStorageService.calculateUserStorage.mockResolvedValue(updatedStorage);

      const result = await service.deleteFile(userId, fileId);

      expect(mockFileMetadataModel.findOne).toHaveBeenCalledWith({
        _id: fileId,
        userId,
      });
      expect(fs.existsSync).toHaveBeenCalled();
      expect(fs.unlinkSync).toHaveBeenCalled();
      expect(mockFileMetadataService.deleteFileMetadata).toHaveBeenCalledWith(fileId);
      expect(mockStorageService.calculateUserStorage).toHaveBeenCalledWith(userId);

      expect(result).toEqual({
        deletedCount: 1,
        totalSizeFreed: 100000,
        newStorageUsage: 900000,
        newUsagePercentage: 0.08,
      });
    });

    it('should throw NotFoundException when file not found', async () => {
      const userId = 'user123';
      const fileId = 'file123';

      mockFileMetadataModel.findOne.mockResolvedValue(null);

      await expect(service.deleteFile(userId, fileId)).rejects.toThrow(NotFoundException);
    });
  });

  describe('deleteMultipleFiles', () => {
    it('should delete multiple files and return delete result', async () => {
      const userId = 'user123';
      const fileIds = ['file1', 'file2', 'file3'];
      const filesToDelete = [
        {
          _id: 'file1',
          userId,
          fileKey: 'media-123.jpg',
          fileName: 'test1.jpg',
          fileType: 'image/jpeg',
          fileSize: 100000,
          mediaType: MediaType.IMAGE,
          messageId: 'message1',
          roomId: 'room1',
          senderId: userId,
          sentAt: new Date(),
        },
        {
          _id: 'file2',
          userId,
          fileKey: 'media-456.mp4',
          fileName: 'test2.mp4',
          fileType: 'video/mp4',
          fileSize: 200000,
          mediaType: MediaType.VIDEO,
          messageId: 'message2',
          roomId: 'room1',
          senderId: userId,
          sentAt: new Date(),
        },
        {
          _id: 'file3',
          userId,
          fileKey: 'media-789.pdf',
          fileName: 'test3.pdf',
          fileType: 'application/pdf',
          fileSize: 300000,
          mediaType: MediaType.DOCUMENT,
          messageId: 'message3',
          roomId: 'room2',
          senderId: userId,
          sentAt: new Date(),
        },
      ];

      const updatedStorage = {
        totalUsage: 400000,
        totalLimit: 1073741824, // 1GB
        usagePercentage: 0.04,
        imageUsage: 0,
        videoUsage: 200000,
        documentUsage: 100000,
        voiceUsage: 100000,
        lastCalculatedAt: new Date(),
      };

      mockFileMetadataModel.find.mockResolvedValue(filesToDelete);
      (fs.existsSync as jest.Mock).mockReturnValue(true);
      mockFileMetadataService.deleteMultipleFileMetadata.mockResolvedValue({
        deletedCount: 3,
        totalSizeFreed: 600000,
      });
      mockStorageService.calculateUserStorage.mockResolvedValue(updatedStorage);

      const result = await service.deleteMultipleFiles(userId, fileIds);

      expect(mockFileMetadataModel.find).toHaveBeenCalledWith({
        _id: { $in: fileIds },
        userId,
      });
      expect(fs.existsSync).toHaveBeenCalledTimes(3);
      expect(fs.unlinkSync).toHaveBeenCalledTimes(3);
      expect(mockFileMetadataService.deleteMultipleFileMetadata).toHaveBeenCalledWith([
        'file1',
        'file2',
        'file3',
      ]);
      expect(mockStorageService.calculateUserStorage).toHaveBeenCalledWith(userId);

      expect(result).toEqual({
        deletedCount: 3,
        totalSizeFreed: 600000,
        newStorageUsage: 400000,
        newUsagePercentage: 0.04,
      });
    });

    it('should return empty result when no files found', async () => {
      const userId = 'user123';
      const fileIds = ['file1', 'file2', 'file3'];

      mockFileMetadataModel.find.mockResolvedValue([]);

      const result = await service.deleteMultipleFiles(userId, fileIds);

      expect(result).toEqual({
        deletedCount: 0,
        totalSizeFreed: 0,
        newStorageUsage: 0,
        newUsagePercentage: 0,
      });
    });
  });
});