import { Test, TestingModule } from '@nestjs/testing';
import { StorageService } from '../services/storage.service';
import { StorageLimitException } from '../exceptions/storage-limit.exception';
import { DEFAULT_STORAGE_LIMIT } from '../constants/storage-limits.constants';

describe('StorageLimitException', () => {
  let storageService: StorageService;

  const mockStorageMetadataModel = {
    findOne: jest.fn(),
    create: jest.fn(),
    findOneAndUpdate: jest.fn(),
    updateOne: jest.fn(),
  };

  const mockFileMetadataModel = {
    find: jest.fn(),
    aggregate: jest.fn(),
    countDocuments: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        StorageService,
        {
          provide: 'storage_metadataModel',
          useValue: mockStorageMetadataModel,
        },
        {
          provide: 'file_metadataModel',
          useValue: mockFileMetadataModel,
        },
      ],
    }).compile();

    storageService = module.get<StorageService>(StorageService);
  });

  it('should be defined', () => {
    expect(storageService).toBeDefined();
  });

  describe('validateFileUpload', () => {
    it('should throw StorageLimitException when upload would exceed limit', async () => {
      // Mock storage usage close to the limit
      const mockStorageUsage = {
        totalUsage: DEFAULT_STORAGE_LIMIT - 1000000, // 1MB less than limit
        totalLimit: DEFAULT_STORAGE_LIMIT,
        usagePercentage: 99.9,
        imageUsage: 500000000,
        videoUsage: 300000000,
        documentUsage: 100000000,
        voiceUsage: 100000000,
        lastCalculatedAt: new Date(),
      };

      jest.spyOn(storageService, 'calculateUserStorage').mockResolvedValue(mockStorageUsage);

      // Try to upload a file larger than the remaining space
      const fileSize = 2000000; // 2MB

      await expect(storageService.validateFileUpload('user123', fileSize)).rejects.toThrow(
        StorageLimitException
      );
    });

    it('should not throw exception when upload is within limit', async () => {
      // Mock storage usage with plenty of space
      const mockStorageUsage = {
        totalUsage: 500000000, // 500MB
        totalLimit: DEFAULT_STORAGE_LIMIT,
        usagePercentage: 50,
        imageUsage: 300000000,
        videoUsage: 100000000,
        documentUsage: 50000000,
        voiceUsage: 50000000,
        lastCalculatedAt: new Date(),
      };

      jest.spyOn(storageService, 'calculateUserStorage').mockResolvedValue(mockStorageUsage);

      // Try to upload a file that fits within the limit
      const fileSize = 10000000; // 10MB

      await expect(storageService.validateFileUpload('user123', fileSize)).resolves.not.toThrow();
    });
  });
});