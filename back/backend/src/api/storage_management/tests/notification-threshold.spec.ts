import { Test, TestingModule } from '@nestjs/testing';
import { StorageNotificationService } from '../services/storage-notification.service';
import { NotificationTemplateService } from '../services/notification-template.service';
import { NotificationEmitterService } from '../../../common/notification_emitter/notification_emitter.service';
import { StorageService } from '../services/storage.service';
import { getModelToken } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { IStorageMetadata } from '../entities/storage_metadata.entity';
import { DEFAULT_STORAGE_LIMIT } from '../constants/storage-limits.constants';

describe('Notification Threshold Tracking', () => {
  let service: StorageNotificationService;
  let notificationTemplateService: NotificationTemplateService;
  let storageMetadataModel: Model<IStorageMetadata>;

  const mockStorageMetadataModel = {
    find: jest.fn(),
    findOne: jest.fn(),
    updateOne: jest.fn(),
    exec: jest.fn(),
  };

  const mockNotificationEmitter = {
    fcmSend: jest.fn(),
  };

  const mockStorageService = {
    calculateUserStorage: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        StorageNotificationService,
        NotificationTemplateService,
        {
          provide: NotificationEmitterService,
          useValue: mockNotificationEmitter,
        },
        {
          provide: StorageService,
          useValue: mockStorageService,
        },
        {
          provide: getModelToken('storage_metadata'),
          useValue: mockStorageMetadataModel,
        },
      ],
    }).compile();

    service = module.get<StorageNotificationService>(StorageNotificationService);
    notificationTemplateService = module.get<NotificationTemplateService>(NotificationTemplateService);
    storageMetadataModel = module.get<Model<IStorageMetadata>>(getModelToken('storage_metadata'));

    // Reset all mocks before each test
    jest.clearAllMocks();
  });

  describe('shouldSendNotification', () => {
    it('should return true if no notification history exists', async () => {
      mockStorageMetadataModel.findOne.mockReturnValue({
        exec: jest.fn().mockResolvedValue({
          notificationHistory: null,
        }),
      });

      const result = await service['shouldSendNotification']('123', 'high_storage', 24);
      expect(result).toBe(true);
    });

    it('should return true if notification type has not been sent before', async () => {
      mockStorageMetadataModel.findOne.mockReturnValue({
        exec: jest.fn().mockResolvedValue({
          notificationHistory: {
            critical_storage: new Date(),
          },
        }),
      });

      const result = await service['shouldSendNotification']('123', 'high_storage', 24);
      expect(result).toBe(true);
    });

    it('should return false if notification was sent recently', async () => {
      const now = new Date();
      const oneHourAgo = new Date(now.getTime() - (1 * 60 * 60 * 1000));
      
      mockStorageMetadataModel.findOne.mockReturnValue({
        exec: jest.fn().mockResolvedValue({
          notificationHistory: {
            high_storage: oneHourAgo,
          },
        }),
      });

      const result = await service['shouldSendNotification']('123', 'high_storage', 24);
      expect(result).toBe(false);
    });

    it('should return true if enough time has passed since last notification', async () => {
      const now = new Date();
      const twentyFiveHoursAgo = new Date(now.getTime() - (25 * 60 * 60 * 1000));
      
      mockStorageMetadataModel.findOne.mockReturnValue({
        exec: jest.fn().mockResolvedValue({
          notificationHistory: {
            high_storage: twentyFiveHoursAgo,
          },
        }),
      });

      const result = await service['shouldSendNotification']('123', 'high_storage', 24);
      expect(result).toBe(true);
    });
  });

  describe('checkForStorageReduction', () => {
    it('should send storage cleared notification when user drops below high threshold', async () => {
      const userId = '123';
      const currentUsagePercentage = 65;
      
      // Mock storage metadata with previous high usage notification
      mockStorageMetadataModel.findOne.mockReturnValue({
        exec: jest.fn().mockResolvedValue({
          notificationHistory: {
            high_storage: new Date(),
          },
        }),
      });
      
      // Mock the notification template service
      const notificationTemplate = {
        title: 'Storage Space Freed',
        body: 'Great job! You\'ve freed up space.',
        data: { level: 'normal' },
      };
      
      jest.spyOn(notificationTemplateService, 'generateStorageClearedNotification')
        .mockReturnValue(notificationTemplate);
      
      // Mock the sendStorageNotification method
      const sendNotificationSpy = jest.spyOn(service as any, 'sendStorageNotification').mockResolvedValue();
      
      await service['checkForStorageReduction'](userId, currentUsagePercentage);
      
      expect(mockStorageMetadataModel.findOne).toHaveBeenCalledWith({ userId });
      expect(notificationTemplateService.generateStorageClearedNotification).toHaveBeenCalledWith(currentUsagePercentage);
      expect(sendNotificationSpy).toHaveBeenCalledWith(
        userId,
        notificationTemplate.title,
        notificationTemplate.body,
        notificationTemplate.data
      );
      
      // Verify that notification history is reset
      expect(mockStorageMetadataModel.updateOne).toHaveBeenCalledWith(
        { userId },
        {
          $set: {
            'notificationHistory.high_storage': null,
            'notificationHistory.critical_storage': null,
            'notificationHistory.full_storage': null,
          },
        }
      );
    });
    
    it('should not send notification if user is still above high threshold', async () => {
      const userId = '123';
      const currentUsagePercentage = 75; // Still above 70% threshold
      
      // Mock storage metadata with previous high usage notification
      mockStorageMetadataModel.findOne.mockReturnValue({
        exec: jest.fn().mockResolvedValue({
          notificationHistory: {
            high_storage: new Date(),
          },
        }),
      });
      
      // Mock the sendStorageNotification method
      const sendNotificationSpy = jest.spyOn(service as any, 'sendStorageNotification').mockResolvedValue();
      
      await service['checkForStorageReduction'](userId, currentUsagePercentage);
      
      expect(mockStorageMetadataModel.findOne).toHaveBeenCalledWith({ userId });
      expect(sendNotificationSpy).not.toHaveBeenCalled();
      expect(mockStorageMetadataModel.updateOne).not.toHaveBeenCalled();
    });
    
    it('should not send notification if user was not previously in high usage state', async () => {
      const userId = '123';
      const currentUsagePercentage = 65;
      
      // Mock storage metadata with no previous high usage notification
      mockStorageMetadataModel.findOne.mockReturnValue({
        exec: jest.fn().mockResolvedValue({
          notificationHistory: {},
        }),
      });
      
      // Mock the sendStorageNotification method
      const sendNotificationSpy = jest.spyOn(service as any, 'sendStorageNotification').mockResolvedValue();
      
      await service['checkForStorageReduction'](userId, currentUsagePercentage);
      
      expect(mockStorageMetadataModel.findOne).toHaveBeenCalledWith({ userId });
      expect(sendNotificationSpy).not.toHaveBeenCalled();
      expect(mockStorageMetadataModel.updateOne).not.toHaveBeenCalled();
    });
  });

  describe('recordNotificationSent', () => {
    it('should update notification history with current date', async () => {
      const userId = '123';
      const notificationType = 'high_storage';
      
      await service['recordNotificationSent'](userId, notificationType);
      
      expect(mockStorageMetadataModel.updateOne).toHaveBeenCalledWith(
        { userId },
        { 
          $set: { 
            [`notificationHistory.${notificationType}`]: expect.any(Date)
          } 
        },
        { upsert: true }
      );
    });
  });

  describe('notification frequency', () => {
    it('should check high storage notifications once per day', async () => {
      const userId = '123';
      const usagePercentage = 75;
      
      // Mock shouldSendNotification to return true
      jest.spyOn(service as any, 'shouldSendNotification').mockResolvedValue(true);
      
      // Mock other methods
      jest.spyOn(service as any, 'sendStorageNotification').mockResolvedValue();
      jest.spyOn(service as any, 'recordNotificationSent').mockResolvedValue();
      
      await service['sendHighStorageNotification'](userId, usagePercentage);
      
      expect(service['shouldSendNotification']).toHaveBeenCalledWith(userId, 'high_storage', 24);
    });
    
    it('should check critical storage notifications twice per day', async () => {
      const userId = '123';
      const usagePercentage = 95;
      
      // Mock shouldSendNotification to return true
      jest.spyOn(service as any, 'shouldSendNotification').mockResolvedValue(true);
      
      // Mock other methods
      jest.spyOn(service as any, 'sendStorageNotification').mockResolvedValue();
      jest.spyOn(service as any, 'recordNotificationSent').mockResolvedValue();
      
      await service['sendCriticalStorageNotification'](userId, usagePercentage);
      
      expect(service['shouldSendNotification']).toHaveBeenCalledWith(userId, 'critical_storage', 12);
    });
  });
});