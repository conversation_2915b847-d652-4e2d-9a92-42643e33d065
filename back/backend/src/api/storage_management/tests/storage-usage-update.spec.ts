import { Test, TestingModule } from '@nestjs/testing';
import { getModelToken } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { StorageService } from '../services/storage.service';
import { FileDeletionService } from '../services/file-deletion.service';
import { FileMetadataService } from '../services/file-metadata.service';
import { IStorageMetadata } from '../entities/storage_metadata.entity';
import { IFileMetadata, MediaType } from '../entities/file_metadata.entity';
import { ModuleRef } from '@nestjs/core';

describe('Storage Usage Updates After File Deletion', () => {
  let storageService: StorageService;
  let fileDeletionService: FileDeletionService;
  let storageMetadataModel: Model<IStorageMetadata>;
  let fileMetadataModel: Model<IFileMetadata>;
  let fileMetadataService: FileMetadataService;
  let moduleRef: ModuleRef;

  const mockUserId = 'user123';
  const mockFileId = 'file123';

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        StorageService,
        FileDeletionService,
        {
          provide: getModelToken('storage_metadata'),
          useValue: {
            findOne: jest.fn(),
            create: jest.fn(),
            findOneAndUpdate: jest.fn(),
            updateOne: jest.fn(),
          },
        },
        {
          provide: getModelToken('file_metadata'),
          useValue: {
            findOne: jest.fn(),
            find: jest.fn(),
            aggregate: jest.fn(),
            countDocuments: jest.fn(),
          },
        },
        {
          provide: FileMetadataService,
          useValue: {
            deleteFileMetadata: jest.fn(),
            deleteMultipleFileMetadata: jest.fn(),
          },
        },
        {
          provide: ModuleRef,
          useValue: {
            get: jest.fn(),
          },
        },
      ],
    }).compile();

    storageService = module.get<StorageService>(StorageService);
    fileDeletionService = module.get<FileDeletionService>(FileDeletionService);
    storageMetadataModel = module.get<Model<IStorageMetadata>>(getModelToken('storage_metadata'));
    fileMetadataModel = module.get<Model<IFileMetadata>>(getModelToken('file_metadata'));
    fileMetadataService = module.get<FileMetadataService>(FileMetadataService);
    moduleRef = module.get<ModuleRef>(ModuleRef);
  });

  describe('trackFileDeletion', () => {
    it('should update storage usage after deleting an image file', async () => {
      const mockStorageMetadata = {
        userId: mockUserId,
        totalUsage: 1000000, // 1MB
        imageUsage: 500000, // 500KB
        videoUsage: 300000, // 300KB
        documentUsage: 100000, // 100KB
        voiceUsage: 100000, // 100KB
        lastCalculatedAt: new Date(),
      };

      const fileSize = 200000; // 200KB
      const mediaType = MediaType.IMAGE;

      jest.spyOn(storageMetadataModel, 'findOne').mockResolvedValue(mockStorageMetadata);
      jest.spyOn(storageMetadataModel, 'updateOne').mockResolvedValue({ acknowledged: true } as any);

      await storageService.trackFileDeletion(mockUserId, fileSize, mediaType);

      expect(storageMetadataModel.updateOne).toHaveBeenCalledWith(
        { userId: mockUserId },
        {
          $set: {
            totalUsage: 800000, // 1MB - 200KB
            imageUsage: 300000, // 500KB - 200KB
            lastCalculatedAt: expect.any(Date),
          },
        }
      );
    });

    it('should update storage usage after deleting a video file', async () => {
      const mockStorageMetadata = {
        userId: mockUserId,
        totalUsage: 2000000, // 2MB
        imageUsage: 500000, // 500KB
        videoUsage: 1000000, // 1MB
        documentUsage: 250000, // 250KB
        voiceUsage: 250000, // 250KB
        lastCalculatedAt: new Date(),
      };

      const fileSize = 500000; // 500KB
      const mediaType = MediaType.VIDEO;

      jest.spyOn(storageMetadataModel, 'findOne').mockResolvedValue(mockStorageMetadata);
      jest.spyOn(storageMetadataModel, 'updateOne').mockResolvedValue({ acknowledged: true } as any);

      await storageService.trackFileDeletion(mockUserId, fileSize, mediaType);

      expect(storageMetadataModel.updateOne).toHaveBeenCalledWith(
        { userId: mockUserId },
        {
          $set: {
            totalUsage: 1500000, // 2MB - 500KB
            videoUsage: 500000, // 1MB - 500KB
            lastCalculatedAt: expect.any(Date),
          },
        }
      );
    });

    it('should handle deletion when storage metadata does not exist', async () => {
      jest.spyOn(storageMetadataModel, 'findOne').mockResolvedValue(null);
      const loggerWarnSpy = jest.spyOn(storageService['logger'], 'warn');

      await storageService.trackFileDeletion(mockUserId, 100000, MediaType.IMAGE);

      expect(loggerWarnSpy).toHaveBeenCalledWith(
        `No storage metadata found for user ${mockUserId} during file deletion`
      );
      expect(storageMetadataModel.updateOne).not.toHaveBeenCalled();
    });

    it('should not allow negative storage usage values', async () => {
      const mockStorageMetadata = {
        userId: mockUserId,
        totalUsage: 100000, // 100KB
        imageUsage: 50000, // 50KB
        videoUsage: 30000, // 30KB
        documentUsage: 10000, // 10KB
        voiceUsage: 10000, // 10KB
        lastCalculatedAt: new Date(),
      };

      const fileSize = 200000; // 200KB (larger than total usage)
      const mediaType = MediaType.IMAGE;

      jest.spyOn(storageMetadataModel, 'findOne').mockResolvedValue(mockStorageMetadata);
      jest.spyOn(storageMetadataModel, 'updateOne').mockResolvedValue({ acknowledged: true } as any);

      await storageService.trackFileDeletion(mockUserId, fileSize, mediaType);

      expect(storageMetadataModel.updateOne).toHaveBeenCalledWith(
        { userId: mockUserId },
        {
          $set: {
            totalUsage: 0, // Should not go negative
            imageUsage: 0, // Should not go negative
            lastCalculatedAt: expect.any(Date),
          },
        }
      );
    });
  });

  describe('trackMultipleFileDeletions', () => {
    it('should update storage usage after deleting multiple files of different types', async () => {
      const mockStorageMetadata = {
        userId: mockUserId,
        totalUsage: 3000000, // 3MB
        imageUsage: 1000000, // 1MB
        videoUsage: 1500000, // 1.5MB
        documentUsage: 300000, // 300KB
        voiceUsage: 200000, // 200KB
        lastCalculatedAt: new Date(),
      };

      const deletions = [
        { fileSize: 200000, mediaType: MediaType.IMAGE }, // 200KB image
        { fileSize: 500000, mediaType: MediaType.VIDEO }, // 500KB video
        { fileSize: 100000, mediaType: MediaType.DOCUMENT }, // 100KB document
        { fileSize: 50000, mediaType: MediaType.VOICE }, // 50KB voice
      ];

      jest.spyOn(storageMetadataModel, 'findOne').mockResolvedValue(mockStorageMetadata);
      jest.spyOn(storageMetadataModel, 'updateOne').mockResolvedValue({ acknowledged: true } as any);

      await storageService.trackMultipleFileDeletions(mockUserId, deletions);

      expect(storageMetadataModel.updateOne).toHaveBeenCalledWith(
        { userId: mockUserId },
        {
          $set: {
            totalUsage: 2150000, // 3MB - 850KB
            imageUsage: 800000, // 1MB - 200KB
            videoUsage: 1000000, // 1.5MB - 500KB
            documentUsage: 200000, // 300KB - 100KB
            voiceUsage: 150000, // 200KB - 50KB
            lastCalculatedAt: expect.any(Date),
          },
        }
      );
    });

    it('should handle multiple files of the same type', async () => {
      const mockStorageMetadata = {
        userId: mockUserId,
        totalUsage: 2000000, // 2MB
        imageUsage: 1500000, // 1.5MB
        videoUsage: 300000, // 300KB
        documentUsage: 100000, // 100KB
        voiceUsage: 100000, // 100KB
        lastCalculatedAt: new Date(),
      };

      const deletions = [
        { fileSize: 300000, mediaType: MediaType.IMAGE }, // 300KB image
        { fileSize: 400000, mediaType: MediaType.IMAGE }, // 400KB image
        { fileSize: 200000, mediaType: MediaType.IMAGE }, // 200KB image
      ];

      jest.spyOn(storageMetadataModel, 'findOne').mockResolvedValue(mockStorageMetadata);
      jest.spyOn(storageMetadataModel, 'updateOne').mockResolvedValue({ acknowledged: true } as any);

      await storageService.trackMultipleFileDeletions(mockUserId, deletions);

      expect(storageMetadataModel.updateOne).toHaveBeenCalledWith(
        { userId: mockUserId },
        {
          $set: {
            totalUsage: 1100000, // 2MB - 900KB
            imageUsage: 600000, // 1.5MB - 900KB
            videoUsage: 300000, // unchanged
            documentUsage: 100000, // unchanged
            voiceUsage: 100000, // unchanged
            lastCalculatedAt: expect.any(Date),
          },
        }
      );
    });
  });

  describe('FileDeletionService integration', () => {
    it('should update storage usage when deleting a single file', async () => {
      const mockFileMetadata = {
        _id: mockFileId,
        userId: mockUserId,
        fileKey: 'test-image.jpg',
        fileName: 'test-image.jpg',
        fileSize: 500000, // 500KB
        mediaType: MediaType.IMAGE,
        messageId: 'msg123',
      };

      const mockStorageUsage = {
        totalUsage: 1500000, // 1.5MB after deletion
        usagePercentage: 75,
      };

      jest.spyOn(fileMetadataModel, 'findOne').mockResolvedValue(mockFileMetadata);
      jest.spyOn(fileDeletionService as any, 'deletePhysicalFile').mockResolvedValue(undefined);
      jest.spyOn(fileDeletionService as any, 'updateMessageReferences').mockResolvedValue(undefined);
      jest.spyOn(storageService, 'trackFileDeletion').mockResolvedValue(undefined);
      jest.spyOn(fileMetadataService, 'deleteFileMetadata').mockResolvedValue(undefined);
      jest.spyOn(storageService, 'calculateUserStorage').mockResolvedValue(mockStorageUsage as any);

      const result = await fileDeletionService.deleteFile(mockUserId, mockFileId);

      expect(storageService.trackFileDeletion).toHaveBeenCalledWith(
        mockUserId,
        mockFileMetadata.fileSize,
        mockFileMetadata.mediaType
      );
      expect(result).toEqual({
        deletedCount: 1,
        totalSizeFreed: mockFileMetadata.fileSize,
        newStorageUsage: mockStorageUsage.totalUsage,
        newUsagePercentage: mockStorageUsage.usagePercentage,
      });
    });

    it('should update storage usage when deleting multiple files', async () => {
      const mockFiles = [
        {
          _id: 'file1',
          userId: mockUserId,
          fileKey: 'image1.jpg',
          fileSize: 300000,
          mediaType: MediaType.IMAGE,
          messageId: 'msg1',
        },
        {
          _id: 'file2',
          userId: mockUserId,
          fileKey: 'video1.mp4',
          fileSize: 800000,
          mediaType: MediaType.VIDEO,
          messageId: 'msg2',
        },
      ];

      const mockStorageUsage = {
        totalUsage: 1000000, // 1MB after deletion
        usagePercentage: 50,
      };

      jest.spyOn(fileMetadataModel, 'find').mockResolvedValue(mockFiles);
      jest.spyOn(fileDeletionService as any, 'deletePhysicalFile').mockResolvedValue(undefined);
      jest.spyOn(fileDeletionService as any, 'updateMultipleMessageReferences').mockResolvedValue(undefined);
      jest.spyOn(storageService, 'trackMultipleFileDeletions').mockResolvedValue(undefined);
      jest.spyOn(fileMetadataService, 'deleteMultipleFileMetadata').mockResolvedValue(undefined);
      jest.spyOn(storageService, 'calculateUserStorage').mockResolvedValue(mockStorageUsage as any);

      const result = await fileDeletionService.deleteMultipleFiles(mockUserId, ['file1', 'file2']);

      expect(storageService.trackMultipleFileDeletions).toHaveBeenCalledWith(mockUserId, [
        { fileSize: 300000, mediaType: MediaType.IMAGE },
        { fileSize: 800000, mediaType: MediaType.VIDEO },
      ]);
      expect(result).toEqual({
        deletedCount: 2,
        totalSizeFreed: 1100000, // 300KB + 800KB
        newStorageUsage: mockStorageUsage.totalUsage,
        newUsagePercentage: mockStorageUsage.usagePercentage,
      });
    });

    it('should handle partial failures in multiple file deletion', async () => {
      const mockFiles = [
        {
          _id: 'file1',
          userId: mockUserId,
          fileKey: 'image1.jpg',
          fileSize: 300000,
          mediaType: MediaType.IMAGE,
          messageId: 'msg1',
        },
        {
          _id: 'file2',
          userId: mockUserId,
          fileKey: 'video1.mp4',
          fileSize: 800000,
          mediaType: MediaType.VIDEO,
          messageId: 'msg2',
        },
      ];

      const mockStorageUsage = {
        totalUsage: 1300000, // Only one file deleted
        usagePercentage: 65,
      };

      jest.spyOn(fileMetadataModel, 'find').mockResolvedValue(mockFiles);
      jest.spyOn(fileDeletionService as any, 'deletePhysicalFile')
        .mockResolvedValueOnce(undefined) // First file succeeds
        .mockRejectedValueOnce(new Error('File not found')); // Second file fails
      jest.spyOn(fileDeletionService as any, 'updateMultipleMessageReferences').mockResolvedValue(undefined);
      jest.spyOn(storageService, 'trackMultipleFileDeletions').mockResolvedValue(undefined);
      jest.spyOn(fileMetadataService, 'deleteMultipleFileMetadata').mockResolvedValue(undefined);
      jest.spyOn(storageService, 'calculateUserStorage').mockResolvedValue(mockStorageUsage as any);

      const result = await fileDeletionService.deleteMultipleFiles(mockUserId, ['file1', 'file2']);

      // Should only track deletion for the successful file
      expect(storageService.trackMultipleFileDeletions).toHaveBeenCalledWith(mockUserId, [
        { fileSize: 300000, mediaType: MediaType.IMAGE },
      ]);
      expect(result.deletedCount).toBe(1);
      expect(result.failedIds).toEqual(['file2']);
    });
  });
});