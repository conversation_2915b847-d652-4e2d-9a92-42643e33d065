import { Test, TestingModule } from '@nestjs/testing';
import { NotificationTemplateService } from '../services/notification-template.service';

describe('NotificationTemplateService', () => {
  let service: NotificationTemplateService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [NotificationTemplateService],
    }).compile();

    service = module.get<NotificationTemplateService>(NotificationTemplateService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('generateHighUsageNotification', () => {
    it('should generate correct high usage notification content', () => {
      const usagePercentage = 75.5;
      const notification = service.generateHighUsageNotification(usagePercentage);
      
      expect(notification.title).toBe('Storage Space Running Low');
      expect(notification.body).toContain('75.5%');
      expect(notification.data.usagePercentage).toBe(usagePercentage);
      expect(notification.data.level).toBe('high');
      expect(notification.data.action_buttons).toHaveLength(2);
      expect(notification.data.action_buttons[0].text).toBe('Manage Storage');
    });
  });

  describe('generateCriticalUsageNotification', () => {
    it('should generate correct critical usage notification content', () => {
      const usagePercentage = 95.2;
      const notification = service.generateCriticalUsageNotification(usagePercentage);
      
      expect(notification.title).toBe('Critical Storage Space Alert');
      expect(notification.body).toContain('95.2%');
      expect(notification.data.usagePercentage).toBe(usagePercentage);
      expect(notification.data.level).toBe('critical');
      expect(notification.data.action_buttons).toHaveLength(2);
      expect(notification.data.suggested_action).toBe('OPEN_STORAGE_MANAGEMENT');
    });
  });

  describe('generateFullStorageNotification', () => {
    it('should generate correct full storage notification content', () => {
      const usagePercentage = 100;
      const notification = service.generateFullStorageNotification(usagePercentage);
      
      expect(notification.title).toBe('Storage Space Full');
      expect(notification.body).toContain('100%');
      expect(notification.data.usagePercentage).toBe(usagePercentage);
      expect(notification.data.level).toBe('full');
      expect(notification.data.action_buttons).toHaveLength(1);
      expect(notification.data.urgent).toBe(true);
    });
  });

  describe('generateStorageClearedNotification', () => {
    it('should generate correct storage cleared notification content', () => {
      const usagePercentage = 65.3;
      const notification = service.generateStorageClearedNotification(usagePercentage);
      
      expect(notification.title).toBe('Storage Space Freed');
      expect(notification.body).toContain('65.3%');
      expect(notification.data.usagePercentage).toBe(usagePercentage);
      expect(notification.data.level).toBe('normal');
    });
  });

  describe('generateStorageDeepLink', () => {
    it('should generate correct deep link without section', () => {
      const deepLink = service.generateStorageDeepLink();
      expect(deepLink).toBe('superup://storage-management');
    });

    it('should generate correct deep link with section', () => {
      const deepLink = service.generateStorageDeepLink('images');
      expect(deepLink).toBe('superup://storage-management/images');
    });
  });
});