import { Test, TestingModule } from '@nestjs/testing';
import { StorageController } from '../controllers/storage.controller';
import { StorageService } from '../services/storage.service';
import { FileDeletionService } from '../services/file-deletion.service';
import { MediaType } from '../entities/file_metadata.entity';
import { DeleteFilesDto } from '../dto/file-management.dto';

describe('StorageController', () => {
  let controller: StorageController;
  let storageService: StorageService;
  let fileDeletionService: FileDeletionService;

  const mockStorageService = {
    calculateUserStorage: jest.fn(),
    updateStorageUsage: jest.fn(),
    checkStorageLimit: jest.fn(),
    validateFileUpload: jest.fn(),
    getUserFiles: jest.fn(),
    getFilesByMediaType: jest.fn(),
  };

  const mockFileDeletionService = {
    deleteFile: jest.fn(),
    deleteMultipleFiles: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [StorageController],
      providers: [
        { provide: StorageService, useValue: mockStorageService },
        { provide: FileDeletionService, useValue: mockFileDeletionService },
      ],
    }).compile();

    controller = module.get<StorageController>(StorageController);
    storageService = module.get<StorageService>(StorageService);
    fileDeletionService = module.get<FileDeletionService>(FileDeletionService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('getStorageUsage', () => {
    it('should return storage usage for the user', async () => {
      const mockUsage = {
        totalUsage: 1000000,
        totalLimit: **********, // 1GB
        usagePercentage: 0.09,
        imageUsage: 500000,
        videoUsage: 300000,
        documentUsage: 100000,
        voiceUsage: 100000,
        lastCalculatedAt: new Date(),
      };

      mockStorageService.calculateUserStorage.mockResolvedValue(mockUsage);

      const result = await controller.getStorageUsage({ user: { userId: 'user123' } });
      expect(result).toEqual(mockUsage);
      expect(mockStorageService.calculateUserStorage).toHaveBeenCalledWith('user123');
    });
  });

  describe('deleteFile', () => {
    it('should delete a single file and return delete result', async () => {
      const mockDeleteResult = {
        deletedCount: 1,
        totalSizeFreed: 500000,
        newStorageUsage: 500000,
        newUsagePercentage: 0.05,
      };

      mockFileDeletionService.deleteFile.mockResolvedValue(mockDeleteResult);

      const result = await controller.deleteFile(
        { user: { userId: 'user123' } },
        'file123'
      );

      expect(result).toEqual(mockDeleteResult);
      expect(mockFileDeletionService.deleteFile).toHaveBeenCalledWith('user123', 'file123');
    });
  });

  describe('deleteMultipleFiles', () => {
    it('should delete multiple files and return delete result', async () => {
      const mockDeleteResult = {
        deletedCount: 3,
        totalSizeFreed: 1500000,
        newStorageUsage: 500000,
        newUsagePercentage: 0.05,
      };

      const deleteFilesDto: DeleteFilesDto = {
        fileIds: ['file1', 'file2', 'file3'],
      };

      mockFileDeletionService.deleteMultipleFiles.mockResolvedValue(mockDeleteResult);

      const result = await controller.deleteMultipleFiles(
        { user: { userId: 'user123' } },
        deleteFilesDto
      );

      expect(result).toEqual(mockDeleteResult);
      expect(mockFileDeletionService.deleteMultipleFiles).toHaveBeenCalledWith(
        'user123',
        deleteFilesDto.fileIds
      );
    });
  });

  describe('getUserFiles', () => {
    it('should return paginated files for the user', async () => {
      const mockPaginatedFiles = {
        files: [
          {
            id: 'file1',
            fileName: 'test.jpg',
            fileType: 'image/jpeg',
            fileSize: 100000,
            mediaType: MediaType.IMAGE,
            roomId: 'room1',
            senderId: 'user1',
            sentAt: new Date(),
          },
        ],
        totalFiles: 1,
        totalSize: 100000,
        page: 1,
        limit: 20,
        totalPages: 1,
      };

      mockStorageService.getUserFiles.mockResolvedValue(mockPaginatedFiles);

      const query = { mediaType: MediaType.IMAGE, page: 1, limit: 20 };
      const result = await controller.getUserFiles({ user: { userId: 'user123' } }, query);

      expect(result).toEqual(mockPaginatedFiles);
      expect(mockStorageService.getUserFiles).toHaveBeenCalledWith('user123', query);
    });
  });
});