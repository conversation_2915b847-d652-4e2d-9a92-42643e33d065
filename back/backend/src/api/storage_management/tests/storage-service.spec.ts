import { Test, TestingModule } from '@nestjs/testing';
import { StorageService } from '../services/storage.service';
import { getModelToken } from '@nestjs/mongoose';
import { MediaType } from '../entities/file_metadata.entity';
import { Model } from 'mongoose';

describe('StorageService', () => {
  let service: StorageService;
  let fileMetadataModel: Model<any>;
  let storageMetadataModel: Model<any>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        StorageService,
        {
          provide: getModelToken('file_metadata'),
          useValue: {
            find: jest.fn(),
            findOne: jest.fn(),
            create: jest.fn(),
            countDocuments: jest.fn(),
            aggregate: jest.fn(),
            lean: jest.fn(),
            sort: jest.fn(),
            skip: jest.fn(),
            limit: jest.fn(),
          },
        },
        {
          provide: getModelToken('storage_metadata'),
          useValue: {
            findOne: jest.fn(),
            create: jest.fn(),
            findOneAndUpdate: jest.fn(),
            updateOne: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<StorageService>(StorageService);
    fileMetadataModel = module.get(getModelToken('file_metadata'));
    storageMetadataModel = module.get(getModelToken('storage_metadata'));
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('getUserFiles', () => {
    it('should return paginated files with default options', async () => {
      // Arrange
      const userId = 'user123';
      const options = {};
      
      const mockFiles = [
        {
          _id: 'file1',
          fileName: 'test.jpg',
          fileType: 'image/jpeg',
          fileSize: 1024000,
          mediaType: MediaType.IMAGE,
          roomId: 'room1',
          senderId: 'sender1',
          sentAt: new Date(),
        },
      ];
      
      const mockCount = 1;
      const mockTotalSize = 1024000;
      
      // Mock the chained methods
      const findMock = jest.fn().mockReturnThis();
      const sortMock = jest.fn().mockReturnThis();
      const skipMock = jest.fn().mockReturnThis();
      const limitMock = jest.fn().mockReturnThis();
      const leanMock = jest.fn().mockResolvedValue(mockFiles);
      
      fileMetadataModel.find = findMock;
      fileMetadataModel.find().sort = sortMock;
      fileMetadataModel.find().sort().skip = skipMock;
      fileMetadataModel.find().sort().skip().limit = limitMock;
      fileMetadataModel.find().sort().skip().limit().lean = leanMock;
      
      fileMetadataModel.countDocuments.mockResolvedValue(mockCount);
      fileMetadataModel.aggregate.mockResolvedValue([{ totalSize: mockTotalSize }]);

      // Act
      const result = await service.getUserFiles(userId, options);

      // Assert
      expect(findMock).toHaveBeenCalledWith({ userId });
      expect(sortMock).toHaveBeenCalledWith({ sentAt: -1 });
      expect(skipMock).toHaveBeenCalledWith(0);
      expect(limitMock).toHaveBeenCalledWith(20);
      expect(result.files.length).toBe(1);
      expect(result.totalFiles).toBe(mockCount);
      expect(result.totalSize).toBe(mockTotalSize);
      expect(result.page).toBe(1);
      expect(result.limit).toBe(20);
      expect(result.totalPages).toBe(1);
    });

    it('should apply filters correctly', async () => {
      // Arrange
      const userId = 'user123';
      const options = {
        mediaType: MediaType.IMAGE,
        page: 2,
        limit: 10,
        sortBy: 'size',
        sortOrder: 'asc',
        startDate: new Date('2023-01-01'),
        endDate: new Date('2023-12-31'),
        roomId: 'room1',
        minSize: 1000,
        maxSize: 10000000,
      };
      
      const mockFiles = [
        {
          _id: 'file1',
          fileName: 'test.jpg',
          fileType: 'image/jpeg',
          fileSize: 1024000,
          mediaType: MediaType.IMAGE,
          roomId: 'room1',
          senderId: 'sender1',
          sentAt: new Date('2023-06-15'),
        },
      ];
      
      const mockCount = 1;
      const mockTotalSize = 1024000;
      
      // Mock the chained methods
      const findMock = jest.fn().mockReturnThis();
      const sortMock = jest.fn().mockReturnThis();
      const skipMock = jest.fn().mockReturnThis();
      const limitMock = jest.fn().mockReturnThis();
      const leanMock = jest.fn().mockResolvedValue(mockFiles);
      
      fileMetadataModel.find = findMock;
      fileMetadataModel.find().sort = sortMock;
      fileMetadataModel.find().sort().skip = skipMock;
      fileMetadataModel.find().sort().skip().limit = limitMock;
      fileMetadataModel.find().sort().skip().limit().lean = leanMock;
      
      fileMetadataModel.countDocuments.mockResolvedValue(mockCount);
      fileMetadataModel.aggregate.mockResolvedValue([{ totalSize: mockTotalSize }]);

      // Act
      const result = await service.getUserFiles(userId, options);

      // Assert
      expect(findMock).toHaveBeenCalledWith({
        userId,
        mediaType: MediaType.IMAGE,
        roomId: 'room1',
        sentAt: {
          $gte: options.startDate,
          $lte: options.endDate,
        },
        fileSize: {
          $gte: options.minSize,
          $lte: options.maxSize,
        },
      });
      expect(sortMock).toHaveBeenCalledWith({ fileSize: 1 });
      expect(skipMock).toHaveBeenCalledWith(10);
      expect(limitMock).toHaveBeenCalledWith(10);
      expect(result.files.length).toBe(1);
      expect(result.page).toBe(2);
      expect(result.limit).toBe(10);
    });
  });

  describe('getFilesByMediaType', () => {
    it('should call getUserFiles with correct parameters', async () => {
      // Arrange
      const userId = 'user123';
      const mediaType = MediaType.VIDEO;
      const page = 2;
      const limit = 15;
      
      const expectedResult = {
        files: [],
        totalFiles: 0,
        totalSize: 0,
        page: 2,
        limit: 15,
        totalPages: 0,
      };
      
      jest.spyOn(service, 'getUserFiles').mockResolvedValue(expectedResult);

      // Act
      const result = await service.getFilesByMediaType(userId, mediaType, page, limit);

      // Assert
      expect(service.getUserFiles).toHaveBeenCalledWith(userId, {
        mediaType,
        page,
        limit,
      });
      expect(result).toEqual(expectedResult);
    });
  });
});