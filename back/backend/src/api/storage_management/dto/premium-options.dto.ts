/**
 * DTO for premium storage options
 */
export class PremiumOptionsDto {
  isAvailable: boolean;
  message: string;
  currentLimit: number;
  premiumLimit?: number;
  upgradePlans?: PremiumPlanDto[];
}

/**
 * DTO for premium plan information
 */
export class PremiumPlanDto {
  id: string;
  name: string;
  storageLimit: number;
  price: number;
  currency: string;
  billingPeriod: 'monthly' | 'yearly';
  features: string[];
}