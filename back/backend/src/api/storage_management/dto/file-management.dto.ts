/**
 * DTO for file listing query parameters
 */
import { MediaType } from "../entities/file_metadata.entity";

export class GetFilesDto {
  mediaType?: MediaType;
  page?: number;
  limit?: number;
  sortBy?: 'size' | 'date';
  sortOrder?: 'asc' | 'desc';
  startDate?: Date;
  endDate?: Date;
  roomId?: string;
  minSize?: number;
  maxSize?: number;
}

/**
 * DTO for file information in responses
 */
export class FileInfoDto {
  id: string;
  fileName: string;
  fileType: string;
  fileSize: number;
  mediaType: MediaType;
  roomId: string;
  roomName?: string;
  senderId: string;
  senderName?: string;
  sentAt: Date;
  thumbnailUrl?: string;
}

/**
 * DTO for paginated file listings
 */
export class PaginatedFilesDto {
  files: FileInfoDto[];
  totalFiles: number;
  totalSize: number;
  page: number;
  limit: number;
  totalPages: number;
}

/**
 * DTO for file deletion requests
 */
export class DeleteFilesDto {
  fileIds: string[];
}

/**
 * DTO for file deletion results
 */
export class DeleteResultDto {
  deletedCount: number;
  totalSizeFreed: number;
  failedIds?: string[];
  newStorageUsage: number;
  newUsagePercentage: number;
}