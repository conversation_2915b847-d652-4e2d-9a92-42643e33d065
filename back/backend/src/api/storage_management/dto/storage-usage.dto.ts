/**
 * DTO for storage usage information
 */
export class StorageUsageDto {
  totalUsage: number;
  totalLimit: number;
  usagePercentage: number;
  imageUsage: number;
  videoUsage: number;
  documentUsage: number;
  voiceUsage: number;
  lastCalculatedAt: Date;
}

/**
 * DTO for storage limit status
 */
export class StorageLimitStatusDto {
  isLimitReached: boolean;
  currentUsage: number;
  totalLimit: number;
  usagePercentage: number;
  remainingBytes: number;
}