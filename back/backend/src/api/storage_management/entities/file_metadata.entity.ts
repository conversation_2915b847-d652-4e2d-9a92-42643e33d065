/**
 * File metadata entity for tracking individual file information
 */
import { Document, Schema } from "mongoose";

export enum MediaType {
  IMAGE = 'image',
  VIDEO = 'video',
  DOCUMENT = 'document',
  VOICE = 'voice'
}

export interface IFileMetadata extends Document {
  userId: Schema.Types.ObjectId;
  fileKey: string;
  fileName: string;
  fileType: string; // mime type
  fileSize: number; // in bytes
  mediaType: MediaType;
  messageId: Schema.Types.ObjectId; // reference to the message containing this file
  roomId: Schema.Types.ObjectId; // chat room where the file was shared
  senderId: Schema.Types.ObjectId; // user who sent the file
  receiverId?: Schema.Types.ObjectId; // user who received the file (for single chats)
  sentAt: Date;
  createdAt: Date;
  updatedAt: Date;
}

export const FileMetadataSchema = new Schema(
  {
    userId: { type: Schema.Types.ObjectId, required: true, ref: "user", index: true },
    fileKey: { type: String, required: true },
    fileName: { type: String, required: true },
    fileType: { type: String, required: true },
    fileSize: { type: Number, required: true },
    mediaType: { 
      type: String, 
      required: true, 
      enum: Object.values(MediaType),
      index: true
    },
    messageId: { type: Schema.Types.ObjectId, required: true, ref: "message", index: true },
    roomId: { type: Schema.Types.ObjectId, required: true, ref: "room", index: true },
    senderId: { type: Schema.Types.ObjectId, required: true, ref: "user" },
    receiverId: { type: Schema.Types.ObjectId, ref: "user" },
    sentAt: { type: Date, default: Date.now, index: true },
  },
  {
    timestamps: true,
  }
);