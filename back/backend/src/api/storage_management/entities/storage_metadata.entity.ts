/**
 * Storage metadata entity for tracking user storage usage
 */
import { Document, Schema } from "mongoose";

export interface IStorageMetadata extends Document {
  userId: Schema.Types.ObjectId;
  totalUsage: number; // in bytes
  imageUsage: number; // in bytes
  videoUsage: number; // in bytes
  documentUsage: number; // in bytes
  voiceUsage: number; // in bytes
  lastCalculatedAt: Date;
  notificationHistory?: {
    high_storage?: Date;
    critical_storage?: Date;
    full_storage?: Date;
  };
  createdAt: Date;
  updatedAt: Date;
}

export const StorageMetadataSchema = new Schema(
  {
    userId: { type: Schema.Types.ObjectId, required: true, ref: "user", unique: true, index: true },
    totalUsage: { type: Number, default: 0 },
    imageUsage: { type: Number, default: 0 },
    videoUsage: { type: Number, default: 0 },
    documentUsage: { type: Number, default: 0 },
    voiceUsage: { type: Number, default: 0 },
    lastCalculatedAt: { type: Date, default: Date.now },
    notificationHistory: {
      high_storage: { type: Date, default: null },
      critical_storage: { type: Date, default: null },
      full_storage: { type: Date, default: null },
    },
  },
  {
    timestamps: true,
  }
);