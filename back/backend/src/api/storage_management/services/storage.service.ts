import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { IStorageMetadata } from '../entities/storage_metadata.entity';
import { IFileMetadata, MediaType } from '../entities/file_metadata.entity';
import { StorageUsageDto, StorageLimitStatusDto } from '../dto/storage-usage.dto';
import { DEFAULT_STORAGE_LIMIT } from '../constants/storage-limits.constants';
import { StorageLimitException } from '../exceptions';
import { GetFilesDto, PaginatedFilesDto, FileInfoDto } from '../dto/file-management.dto';

/**
 * Service for managing user storage data
 * Handles storage usage calculation, tracking, and limit enforcement
 */
@Injectable()
export class StorageService {
  private readonly logger = new Logger(StorageService.name);

  constructor(
    @InjectModel('storage_metadata')
    private readonly storageMetadataModel: Model<IStorageMetadata>,
    @InjectModel('file_metadata')
    private readonly fileMetadataModel: Model<IFileMetadata>,
  ) {}

  /**
   * Calculate and return user's storage usage
   * @param userId User ID
   * @returns Storage usage information
   */
  async calculateUserStorage(userId: string): Promise<StorageUsageDto> {
    try {
      // Get existing storage metadata or create new one
      let storageMetadata = await this.storageMetadataModel.findOne({ userId });
      
      if (!storageMetadata) {
        // If no storage metadata exists, create a new one with zero usage
        storageMetadata = await this.storageMetadataModel.create({
          userId,
          totalUsage: 0,
          imageUsage: 0,
          videoUsage: 0,
          documentUsage: 0,
          voiceUsage: 0,
          lastCalculatedAt: new Date(),
        });
      } else {
        // If metadata exists but is older than 1 hour, recalculate
        const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);
        if (storageMetadata.lastCalculatedAt < oneHourAgo) {
          await this.updateStorageUsage(userId);
          storageMetadata = await this.storageMetadataModel.findOne({ userId });
        }
      }

      // Calculate usage percentage
      const usagePercentage = (storageMetadata.totalUsage / DEFAULT_STORAGE_LIMIT) * 100;

      return {
        totalUsage: storageMetadata.totalUsage,
        totalLimit: DEFAULT_STORAGE_LIMIT,
        usagePercentage: parseFloat(usagePercentage.toFixed(2)),
        imageUsage: storageMetadata.imageUsage,
        videoUsage: storageMetadata.videoUsage,
        documentUsage: storageMetadata.documentUsage,
        voiceUsage: storageMetadata.voiceUsage,
        lastCalculatedAt: storageMetadata.lastCalculatedAt,
      };
    } catch (error) {
      this.logger.error(`Error calculating storage for user ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Update user's storage usage by recalculating from file metadata
   * @param userId User ID
   */
  async updateStorageUsage(userId: string): Promise<void> {
    try {
      // Calculate usage by media type
      const [imageUsage, videoUsage, documentUsage, voiceUsage] = await Promise.all([
        this.calculateUsageByType(userId, MediaType.IMAGE),
        this.calculateUsageByType(userId, MediaType.VIDEO),
        this.calculateUsageByType(userId, MediaType.DOCUMENT),
        this.calculateUsageByType(userId, MediaType.VOICE),
      ]);

      // Calculate total usage
      const totalUsage = imageUsage + videoUsage + documentUsage + voiceUsage;

      // Update storage metadata
      await this.storageMetadataModel.findOneAndUpdate(
        { userId },
        {
          totalUsage,
          imageUsage,
          videoUsage,
          documentUsage,
          voiceUsage,
          lastCalculatedAt: new Date(),
        },
        { upsert: true, new: true },
      );
    } catch (error) {
      this.logger.error(`Error updating storage usage for user ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Calculate storage usage for a specific media type
   * @param userId User ID
   * @param mediaType Media type (image, video, document, voice)
   * @returns Total size in bytes
   */
  private async calculateUsageByType(userId: string, mediaType: MediaType): Promise<number> {
    try {
      const result = await this.fileMetadataModel.aggregate([
        { $match: { userId, mediaType } },
        { $group: { _id: null, totalSize: { $sum: '$fileSize' } } },
      ]);

      return result.length > 0 ? result[0].totalSize : 0;
    } catch (error) {
      this.logger.error(`Error calculating ${mediaType} usage for user ${userId}:`, error);
      return 0;
    }
  }

  /**
   * Check if user has reached storage limit
   * @param userId User ID
   * @returns Storage limit status
   */
  async checkStorageLimit(userId: string): Promise<StorageLimitStatusDto> {
    try {
      const storageUsage = await this.calculateUserStorage(userId);
      
      const isLimitReached = storageUsage.totalUsage >= DEFAULT_STORAGE_LIMIT;
      const remainingBytes = Math.max(0, DEFAULT_STORAGE_LIMIT - storageUsage.totalUsage);

      return {
        isLimitReached,
        currentUsage: storageUsage.totalUsage,
        totalLimit: DEFAULT_STORAGE_LIMIT,
        usagePercentage: storageUsage.usagePercentage,
        remainingBytes,
      };
    } catch (error) {
      this.logger.error(`Error checking storage limit for user ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Validate if a file can be uploaded based on storage limits
   * @param userId User ID
   * @param fileSize File size in bytes
   * @throws StorageLimitException if storage limit would be exceeded
   */
  async validateFileUpload(userId: string, fileSize: number): Promise<void> {
    try {
      // Get current storage usage
      const storageUsage = await this.calculateUserStorage(userId);
      
      // Check if adding this file would exceed the limit
      const newTotalUsage = storageUsage.totalUsage + fileSize;
      
      if (newTotalUsage > DEFAULT_STORAGE_LIMIT) {
        // Calculate limit status for the error response
        const limitStatus: StorageLimitStatusDto = {
          isLimitReached: true,
          currentUsage: storageUsage.totalUsage,
          totalLimit: DEFAULT_STORAGE_LIMIT,
          usagePercentage: storageUsage.usagePercentage,
          remainingBytes: Math.max(0, DEFAULT_STORAGE_LIMIT - storageUsage.totalUsage),
        };
        
        // Throw custom exception with detailed information
        throw new StorageLimitException(limitStatus);
      }
    } catch (error) {
      if (error instanceof StorageLimitException) {
        throw error;
      }
      this.logger.error(`Error validating file upload for user ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Track new file upload and update storage usage
   * @param userId User ID
   * @param fileSize File size in bytes
   * @param mediaType Media type
   */
  async trackFileUpload(userId: string, fileSize: number, mediaType: MediaType): Promise<void> {
    try {
      // Get current storage metadata
      let storageMetadata = await this.storageMetadataModel.findOne({ userId });
      
      if (!storageMetadata) {
        // If no storage metadata exists, create a new one
        storageMetadata = await this.storageMetadataModel.create({
          userId,
          totalUsage: fileSize,
          imageUsage: mediaType === MediaType.IMAGE ? fileSize : 0,
          videoUsage: mediaType === MediaType.VIDEO ? fileSize : 0,
          documentUsage: mediaType === MediaType.DOCUMENT ? fileSize : 0,
          voiceUsage: mediaType === MediaType.VOICE ? fileSize : 0,
          lastCalculatedAt: new Date(),
        });
      } else {
        // Update existing storage metadata
        const update: any = {
          totalUsage: storageMetadata.totalUsage + fileSize,
          lastCalculatedAt: new Date(),
        };

        // Update specific media type usage
        switch (mediaType) {
          case MediaType.IMAGE:
            update.imageUsage = storageMetadata.imageUsage + fileSize;
            break;
          case MediaType.VIDEO:
            update.videoUsage = storageMetadata.videoUsage + fileSize;
            break;
          case MediaType.DOCUMENT:
            update.documentUsage = storageMetadata.documentUsage + fileSize;
            break;
          case MediaType.VOICE:
            update.voiceUsage = storageMetadata.voiceUsage + fileSize;
            break;
        }

        await this.storageMetadataModel.updateOne({ userId }, { $set: update });
      }
    } catch (error) {
      this.logger.error(`Error tracking file upload for user ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Get user files with pagination and filtering
   * @param userId User ID
   * @param options Query options for filtering and pagination
   * @returns Paginated list of files with metadata
   */
  async getUserFiles(userId: string, options: GetFilesDto): Promise<PaginatedFilesDto> {
    try {
      const {
        mediaType,
        page = 1,
        limit = 20,
        sortBy = 'date',
        sortOrder = 'desc',
        startDate,
        endDate,
        roomId,
        minSize,
        maxSize,
      } = options;

      // Build query filters
      const filter: any = { userId };
      
      if (mediaType) {
        filter.mediaType = mediaType;
      }
      
      if (roomId) {
        filter.roomId = roomId;
      }
      
      if (startDate || endDate) {
        filter.sentAt = {};
        if (startDate) {
          filter.sentAt.$gte = new Date(startDate);
        }
        if (endDate) {
          filter.sentAt.$lte = new Date(endDate);
        }
      }
      
      if (minSize || maxSize) {
        filter.fileSize = {};
        if (minSize) {
          filter.fileSize.$gte = minSize;
        }
        if (maxSize) {
          filter.fileSize.$lte = maxSize;
        }
      }

      // Determine sort options
      const sortOptions: any = {};
      if (sortBy === 'size') {
        sortOptions.fileSize = sortOrder === 'asc' ? 1 : -1;
      } else {
        // Default sort by date
        sortOptions.sentAt = sortOrder === 'asc' ? 1 : -1;
      }

      // Calculate pagination
      const skip = (page - 1) * limit;
      
      // Execute query with pagination
      const [files, totalFiles, totalSize] = await Promise.all([
        this.fileMetadataModel
          .find(filter)
          .sort(sortOptions)
          .skip(skip)
          .limit(limit)
          .lean(),
        this.fileMetadataModel.countDocuments(filter),
        this.calculateTotalSize(filter),
      ]);

      // Map to DTO format
      const fileInfos: FileInfoDto[] = files.map(file => ({
        id: file._id.toString(),
        fileName: file.fileName,
        fileType: file.fileType,
        fileSize: file.fileSize,
        mediaType: file.mediaType,
        roomId: file.roomId.toString(),
        senderId: file.senderId.toString(),
        sentAt: file.sentAt,
        // Note: roomName and senderName would require additional lookups
        // which could be implemented if needed
      }));

      return {
        files: fileInfos,
        totalFiles,
        totalSize,
        page,
        limit,
        totalPages: Math.ceil(totalFiles / limit),
      };
    } catch (error) {
      this.logger.error(`Error getting user files for user ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Get files by media type with pagination
   * @param userId User ID
   * @param mediaType Media type to filter by
   * @param page Page number
   * @param limit Items per page
   * @returns Paginated list of files of the specified media type
   */
  async getFilesByMediaType(
    userId: string,
    mediaType: MediaType,
    page = 1,
    limit = 20
  ): Promise<PaginatedFilesDto> {
    return this.getUserFiles(userId, { mediaType, page, limit });
  }

  /**
   * Track file deletion and update storage usage
   * @param userId User ID
   * @param fileSize File size in bytes
   * @param mediaType Media type
   */
  async trackFileDeletion(userId: string, fileSize: number, mediaType: MediaType): Promise<void> {
    try {
      // Get current storage metadata
      const storageMetadata = await this.storageMetadataModel.findOne({ userId });
      
      if (!storageMetadata) {
        this.logger.warn(`No storage metadata found for user ${userId} during file deletion`);
        return;
      }

      // Update existing storage metadata by subtracting the deleted file size
      const update: any = {
        totalUsage: Math.max(0, storageMetadata.totalUsage - fileSize),
        lastCalculatedAt: new Date(),
      };

      // Update specific media type usage
      switch (mediaType) {
        case MediaType.IMAGE:
          update.imageUsage = Math.max(0, storageMetadata.imageUsage - fileSize);
          break;
        case MediaType.VIDEO:
          update.videoUsage = Math.max(0, storageMetadata.videoUsage - fileSize);
          break;
        case MediaType.DOCUMENT:
          update.documentUsage = Math.max(0, storageMetadata.documentUsage - fileSize);
          break;
        case MediaType.VOICE:
          update.voiceUsage = Math.max(0, storageMetadata.voiceUsage - fileSize);
          break;
      }

      await this.storageMetadataModel.updateOne({ userId }, { $set: update });
      
      this.logger.log(`Updated storage usage for user ${userId} after deleting ${mediaType} file of size ${fileSize} bytes`);
    } catch (error) {
      this.logger.error(`Error tracking file deletion for user ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Track multiple file deletions and update storage usage
   * @param userId User ID
   * @param deletions Array of file deletion info
   */
  async trackMultipleFileDeletions(
    userId: string, 
    deletions: Array<{ fileSize: number; mediaType: MediaType }>
  ): Promise<void> {
    try {
      // Get current storage metadata
      const storageMetadata = await this.storageMetadataModel.findOne({ userId });
      
      if (!storageMetadata) {
        this.logger.warn(`No storage metadata found for user ${userId} during multiple file deletion`);
        return;
      }

      // Calculate total size freed by media type
      let totalSizeFreed = 0;
      let imageSizeFreed = 0;
      let videoSizeFreed = 0;
      let documentSizeFreed = 0;
      let voiceSizeFreed = 0;

      for (const deletion of deletions) {
        totalSizeFreed += deletion.fileSize;
        
        switch (deletion.mediaType) {
          case MediaType.IMAGE:
            imageSizeFreed += deletion.fileSize;
            break;
          case MediaType.VIDEO:
            videoSizeFreed += deletion.fileSize;
            break;
          case MediaType.DOCUMENT:
            documentSizeFreed += deletion.fileSize;
            break;
          case MediaType.VOICE:
            voiceSizeFreed += deletion.fileSize;
            break;
        }
      }

      // Update storage metadata
      const update = {
        totalUsage: Math.max(0, storageMetadata.totalUsage - totalSizeFreed),
        imageUsage: Math.max(0, storageMetadata.imageUsage - imageSizeFreed),
        videoUsage: Math.max(0, storageMetadata.videoUsage - videoSizeFreed),
        documentUsage: Math.max(0, storageMetadata.documentUsage - documentSizeFreed),
        voiceUsage: Math.max(0, storageMetadata.voiceUsage - voiceSizeFreed),
        lastCalculatedAt: new Date(),
      };

      await this.storageMetadataModel.updateOne({ userId }, { $set: update });
      
      this.logger.log(`Updated storage usage for user ${userId} after deleting ${deletions.length} files totaling ${totalSizeFreed} bytes`);
    } catch (error) {
      this.logger.error(`Error tracking multiple file deletions for user ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Calculate total size of files matching a filter
   * @param filter Query filter
   * @returns Total size in bytes
   */
  private async calculateTotalSize(filter: any): Promise<number> {
    try {
      const result = await this.fileMetadataModel.aggregate([
        { $match: filter },
        { $group: { _id: null, totalSize: { $sum: '$fileSize' } } },
      ]);

      return result.length > 0 ? result[0].totalSize : 0;
    } catch (error) {
      this.logger.error('Error calculating total file size:', error);
      return 0;
    }
  }
}