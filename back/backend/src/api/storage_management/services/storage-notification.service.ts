import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Cron, CronExpression } from '@nestjs/schedule';
import { NotificationEmitterService } from '../../../common/notification_emitter/notification_emitter.service';
import { IStorageMetadata } from '../entities/storage_metadata.entity';
import { StorageService } from './storage.service';
import { DEFAULT_STORAGE_LIMIT } from '../constants/storage-limits.constants';
import { NotificationTemplateService } from './notification-template.service';

/**
 * Service for managing storage-related notifications
 * Handles checking storage thresholds and sending appropriate notifications
 */
@Injectable()
export class StorageNotificationService {
  private readonly logger = new Logger(StorageNotificationService.name);

  // Notification thresholds
  private readonly HIGH_USAGE_THRESHOLD = 70; // 70% of storage limit
  private readonly CRITICAL_USAGE_THRESHOLD = 90; // 90% of storage limit
  private readonly FULL_USAGE_THRESHOLD = 100; // 100% of storage limit

  constructor(
    private readonly storageService: StorageService,
    private readonly notificationEmitter: NotificationEmitterService,
    private readonly notificationTemplateService: NotificationTemplateService,
    @InjectModel('storage_metadata')
    private readonly storageMetadataModel: Model<IStorageMetadata>,
  ) {}

  /**
   * Scheduled job to check storage thresholds and send notifications
   * Runs once per day at midnight
   */
  @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT)
  async checkStorageThresholds() {
    try {
      this.logger.log('Running scheduled storage threshold check');
      
      // Get all users with storage metadata
      const allStorageMetadata = await this.storageMetadataModel.find().exec();
      
      for (const metadata of allStorageMetadata) {
        await this.checkAndNotifyUser(metadata.userId.toString());
      }
      
      this.logger.log('Completed storage threshold check');
    } catch (error) {
      this.logger.error('Error checking storage thresholds:', error);
    }
  }

  /**
   * Check storage usage for a specific user and send notifications if needed
   * @param userId User ID to check
   */
  async checkAndNotifyUser(userId: string): Promise<void> {
    try {
      // Get current storage usage
      const storageUsage = await this.storageService.calculateUserStorage(userId);
      
      // Calculate usage percentage
      const usagePercentage = (storageUsage.totalUsage / DEFAULT_STORAGE_LIMIT) * 100;
      
      // Check if we need to send a notification based on thresholds
      if (usagePercentage >= this.FULL_USAGE_THRESHOLD) {
        await this.sendStorageFullNotification(userId, usagePercentage);
      } else if (usagePercentage >= this.CRITICAL_USAGE_THRESHOLD) {
        await this.sendCriticalStorageNotification(userId, usagePercentage);
      } else if (usagePercentage >= this.HIGH_USAGE_THRESHOLD) {
        await this.sendHighStorageNotification(userId, usagePercentage);
      } else {
        // Check if user has recently freed up significant space
        await this.checkForStorageReduction(userId, usagePercentage);
      }
    } catch (error) {
      this.logger.error(`Error checking storage for user ${userId}:`, error);
    }
  }

  /**
   * Send notification for high storage usage (70-89%)
   * @param userId User ID
   * @param usagePercentage Current usage percentage
   */
  private async sendHighStorageNotification(userId: string, usagePercentage: number): Promise<void> {
    try {
      // Check if we've already sent a notification in the last 24 hours
      const shouldSendNotification = await this.shouldSendNotification(userId, 'high_storage', 24);
      
      if (shouldSendNotification) {
        // Generate notification content using template service
        const notification = this.notificationTemplateService.generateHighUsageNotification(usagePercentage);
        
        await this.sendStorageNotification(
          userId, 
          notification.title, 
          notification.body, 
          notification.data
        );
        
        // Record that we sent a notification
        await this.recordNotificationSent(userId, 'high_storage');
      }
    } catch (error) {
      this.logger.error(`Error sending high storage notification to user ${userId}:`, error);
    }
  }

  /**
   * Send notification for critical storage usage (90-99%)
   * @param userId User ID
   * @param usagePercentage Current usage percentage
   */
  private async sendCriticalStorageNotification(userId: string, usagePercentage: number): Promise<void> {
    try {
      // For critical storage, we send notifications twice daily (every 12 hours)
      const shouldSendNotification = await this.shouldSendNotification(userId, 'critical_storage', 12);
      
      if (shouldSendNotification) {
        // Generate notification content using template service
        const notification = this.notificationTemplateService.generateCriticalUsageNotification(usagePercentage);
        
        await this.sendStorageNotification(
          userId, 
          notification.title, 
          notification.body, 
          notification.data
        );
        
        // Record that we sent a notification
        await this.recordNotificationSent(userId, 'critical_storage');
      }
    } catch (error) {
      this.logger.error(`Error sending critical storage notification to user ${userId}:`, error);
    }
  }

  /**
   * Send notification for full storage (100%)
   * @param userId User ID
   * @param usagePercentage Current usage percentage
   */
  private async sendStorageFullNotification(userId: string, usagePercentage: number): Promise<void> {
    try {
      // For full storage, we only send one notification when they hit 100%
      const shouldSendNotification = await this.shouldSendNotification(userId, 'full_storage', 24);
      
      if (shouldSendNotification) {
        // Generate notification content using template service
        const notification = this.notificationTemplateService.generateFullStorageNotification(usagePercentage);
        
        await this.sendStorageNotification(
          userId, 
          notification.title, 
          notification.body, 
          notification.data
        );
        
        // Record that we sent a notification
        await this.recordNotificationSent(userId, 'full_storage');
      }
    } catch (error) {
      this.logger.error(`Error sending storage full notification to user ${userId}:`, error);
    }
  }

  /**
   * Send a storage notification to a user
   * @param userId User ID
   * @param title Notification title
   * @param body Notification body
   * @param data Additional data to include in the notification
   */
  private async sendStorageNotification(
    userId: string, 
    title: string, 
    body: string, 
    data: any
  ): Promise<void> {
    try {
      // Get user's device tokens (implementation depends on your user device service)
      const userDeviceTokens = await this.getUserDeviceTokens(userId);
      
      if (userDeviceTokens && userDeviceTokens.length > 0) {
        // Send notification using the notification emitter service
        this.notificationEmitter.fcmSend({
          tokens: userDeviceTokens,
          title: title,
          body: body,
          tag: 'storage_management',
          data: {
            ...data,
            click_action: 'STORAGE_MANAGEMENT',
          },
        });
        
        this.logger.log(`Sent storage notification to user ${userId}`);
      }
    } catch (error) {
      this.logger.error(`Error sending notification to user ${userId}:`, error);
    }
  }

  /**
   * Check if we should send a notification based on when the last one was sent
   * @param userId User ID
   * @param notificationType Type of notification
   * @param hoursSinceLastNotification Hours that should have passed since last notification
   * @returns Boolean indicating if notification should be sent
   */
  private async shouldSendNotification(
    userId: string,
    notificationType: string,
    hoursSinceLastNotification: number
  ): Promise<boolean> {
    try {
      // Get storage metadata
      const storageMetadata = await this.storageMetadataModel.findOne({ userId }).exec();
      
      if (!storageMetadata) {
        return true; // If no metadata exists, we should send a notification
      }
      
      // Check if notification history exists
      if (!storageMetadata.notificationHistory) {
        return true;
      }
      
      // Check if this type of notification has been sent before
      const lastNotification = storageMetadata.notificationHistory[notificationType];
      
      if (!lastNotification) {
        return true;
      }
      
      // Check if enough time has passed since the last notification
      const hoursSinceLast = (Date.now() - lastNotification.getTime()) / (1000 * 60 * 60);
      
      return hoursSinceLast >= hoursSinceLastNotification;
    } catch (error) {
      this.logger.error(`Error checking notification history for user ${userId}:`, error);
      return false;
    }
  }

  /**
   * Record that a notification was sent
   * @param userId User ID
   * @param notificationType Type of notification
   */
  private async recordNotificationSent(userId: string, notificationType: string): Promise<void> {
    try {
      // Update the notification history in storage metadata
      await this.storageMetadataModel.updateOne(
        { userId },
        { 
          $set: { 
            [`notificationHistory.${notificationType}`]: new Date() 
          } 
        },
        { upsert: true }
      );
    } catch (error) {
      this.logger.error(`Error recording notification for user ${userId}:`, error);
    }
  }

  /**
   * Check if user has recently freed up significant storage space
   * @param userId User ID
   * @param currentUsagePercentage Current usage percentage
   */
  private async checkForStorageReduction(userId: string, currentUsagePercentage: number): Promise<void> {
    try {
      // Get storage metadata
      const storageMetadata = await this.storageMetadataModel.findOne({ userId }).exec();
      
      if (!storageMetadata) {
        return; // No previous data to compare with
      }
      
      // Check if we have notification history
      if (!storageMetadata.notificationHistory) {
        return;
      }
      
      // Check if user was previously in a high/critical/full state
      const wasInHighUsageState = storageMetadata.notificationHistory.high_storage || 
                                 storageMetadata.notificationHistory.critical_storage || 
                                 storageMetadata.notificationHistory.full_storage;
      
      // Define what constitutes a "significant" storage reduction
      // If user was in high usage state but now is below the high threshold
      if (wasInHighUsageState && currentUsagePercentage < this.HIGH_USAGE_THRESHOLD) {
        this.logger.log(`User ${userId} has freed up significant storage space. Current usage: ${currentUsagePercentage.toFixed(1)}%`);
        
        // Generate notification content using template service
        const notification = this.notificationTemplateService.generateStorageClearedNotification(currentUsagePercentage);
        
        await this.sendStorageNotification(
          userId, 
          notification.title, 
          notification.body, 
          notification.data
        );
        
        // Reset notification history for high/critical/full states
        // This prevents sending notifications until user reaches thresholds again
        await this.storageMetadataModel.updateOne(
          { userId },
          { 
            $set: { 
              'notificationHistory.high_storage': null,
              'notificationHistory.critical_storage': null,
              'notificationHistory.full_storage': null
            } 
          }
        );
        
        this.logger.log(`Reset notification thresholds for user ${userId} after storage reduction`);
      } else if (wasInHighUsageState) {
        // User was in high usage state but hasn't cleared enough storage yet
        // Log this for monitoring purposes
        this.logger.log(`User ${userId} has reduced storage but still above threshold. Current usage: ${currentUsagePercentage.toFixed(1)}%`);
      }
    } catch (error) {
      this.logger.error(`Error checking storage reduction for user ${userId}:`, error);
    }
  }

  /**
   * Get user's device tokens for sending notifications
   * This is a placeholder - implementation depends on your user device service
   * @param userId User ID
   * @returns Array of device tokens
   */
  private async getUserDeviceTokens(userId: string): Promise<string[]> {
    // This is a placeholder implementation
    // In a real application, you would query your user device service
    // to get the user's FCM tokens
    
    // For now, we'll just return an empty array
    // Replace this with actual implementation
    return [];
  }
}