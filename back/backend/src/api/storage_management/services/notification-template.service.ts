import { Injectable } from '@nestjs/common';

/**
 * Service for generating storage notification templates
 * Creates standardized notification content with usage information and action links
 */
@Injectable()
export class NotificationTemplateService {
  /**
   * Generate notification content for high storage usage (70-89%)
   * @param usagePercentage Current storage usage percentage
   * @returns Object containing title, body, and data for notification
   */
  generateHighUsageNotification(usagePercentage: number): {
    title: string;
    body: string;
    data: any;
  } {
    return {
      title: 'Storage Space Running Low',
      body: `You've used ${usagePercentage.toFixed(1)}% of your storage space. Free up space or upgrade soon.`,
      data: {
        type: 'storage_warning',
        usagePercentage: usagePercentage,
        level: 'high',
        click_action: 'STORAGE_MANAGEMENT',
        action_buttons: [
          {
            text: 'Manage Storage',
            action: 'OPEN_STORAGE_MANAGEMENT',
          },
          {
            text: 'Dismiss',
            action: 'DISMISS',
          },
        ],
      },
    };
  }

  /**
   * Generate notification content for critical storage usage (90-99%)
   * @param usagePercentage Current storage usage percentage
   * @returns Object containing title, body, and data for notification
   */
  generateCriticalUsageNotification(usagePercentage: number): {
    title: string;
    body: string;
    data: any;
  } {
    return {
      title: 'Critical Storage Space Alert',
      body: `You've used ${usagePercentage.toFixed(1)}% of your storage space. Free up space now to avoid upload issues.`,
      data: {
        type: 'storage_warning',
        usagePercentage: usagePercentage,
        level: 'critical',
        click_action: 'STORAGE_MANAGEMENT',
        action_buttons: [
          {
            text: 'Free Up Space',
            action: 'OPEN_STORAGE_MANAGEMENT',
          },
          {
            text: 'Dismiss',
            action: 'DISMISS',
          },
        ],
        suggested_action: 'OPEN_STORAGE_MANAGEMENT',
      },
    };
  }

  /**
   * Generate notification content for full storage (100%)
   * @param usagePercentage Current storage usage percentage
   * @returns Object containing title, body, and data for notification
   */
  generateFullStorageNotification(usagePercentage: number): {
    title: string;
    body: string;
    data: any;
  } {
    return {
      title: 'Storage Space Full',
      body: `You've reached 100% of your storage limit. You cannot upload new files until you free up space.`,
      data: {
        type: 'storage_warning',
        usagePercentage: usagePercentage,
        level: 'full',
        click_action: 'STORAGE_MANAGEMENT',
        action_buttons: [
          {
            text: 'Free Up Space Now',
            action: 'OPEN_STORAGE_MANAGEMENT',
          },
        ],
        suggested_action: 'OPEN_STORAGE_MANAGEMENT',
        urgent: true,
      },
    };
  }

  /**
   * Generate notification content for storage cleared (when user frees up significant space)
   * @param usagePercentage Current storage usage percentage
   * @returns Object containing title, body, and data for notification
   */
  generateStorageClearedNotification(usagePercentage: number): {
    title: string;
    body: string;
    data: any;
  } {
    return {
      title: 'Storage Space Freed',
      body: `Great job! You've freed up space and are now at ${usagePercentage.toFixed(1)}% of your storage limit.`,
      data: {
        type: 'storage_info',
        usagePercentage: usagePercentage,
        level: 'normal',
        click_action: 'STORAGE_MANAGEMENT',
      },
    };
  }

  /**
   * Generate deep link URL for storage management
   * @param section Optional specific section to navigate to (images, videos, etc.)
   * @returns Deep link URL string
   */
  generateStorageDeepLink(section?: string): string {
    let deepLink = 'superup://storage-management';
    
    if (section) {
      deepLink += `/${section}`;
    }
    
    return deepLink;
  }
}