import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { ModuleRef } from '@nestjs/core';
import { Model } from 'mongoose';
import { IFileMetadata } from '../entities/file_metadata.entity';
import { FileMetadataService } from './file-metadata.service';
import { StorageService } from './storage.service';
import { DeleteResultDto } from '../dto/file-management.dto';
import * as fs from 'fs';
import * as path from 'path';
import * as root from 'app-root-path';

/**
 * Service for handling file deletion operations
 * Manages both metadata and physical file removal
 */
@Injectable()
export class FileDeletionService {
  private readonly logger = new Logger(FileDeletionService.name);

  constructor(
    @InjectModel('file_metadata')
    private readonly fileMetadataModel: Model<IFileMetadata>,
    private readonly fileMetadataService: FileMetadataService,
    private readonly storageService: StorageService,
    private readonly moduleRef: ModuleRef,
  ) {}

  /**
   * Delete a single file by ID
   * @param userId User ID
   * @param fileId File ID to delete
   * @returns Delete result with updated storage information
   */
  async deleteFile(userId: string, fileId: string): Promise<DeleteResultDto> {
    try {
      // Get file metadata to ensure it belongs to the user
      const fileMetadata = await this.fileMetadataModel.findOne({
        _id: fileId,
        userId,
      });

      if (!fileMetadata) {
        throw new NotFoundException(`File with ID ${fileId} not found or does not belong to the user`);
      }

      // Delete the physical file
      await this.deletePhysicalFile(fileMetadata.fileKey);

      // Update message references to remove file attachment
      await this.updateMessageReferences(fileMetadata);

      // Track file deletion in storage usage
      await this.storageService.trackFileDeletion(userId, fileMetadata.fileSize, fileMetadata.mediaType);

      // Delete the file metadata
      await this.fileMetadataService.deleteFileMetadata(fileId);

      // Get updated storage usage
      const updatedStorage = await this.storageService.calculateUserStorage(userId);

      return {
        deletedCount: 1,
        totalSizeFreed: fileMetadata.fileSize,
        newStorageUsage: updatedStorage.totalUsage,
        newUsagePercentage: updatedStorage.usagePercentage,
      };
    } catch (error) {
      this.logger.error(`Error deleting file ${fileId} for user ${userId}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Delete multiple files by IDs
   * @param userId User ID
   * @param fileIds Array of file IDs to delete
   * @returns Delete result with updated storage information
   */
  async deleteMultipleFiles(userId: string, fileIds: string[]): Promise<DeleteResultDto> {
    try {
      // Get file metadata to ensure they belong to the user
      const filesToDelete = await this.fileMetadataModel.find({
        _id: { $in: fileIds },
        userId,
      });

      if (filesToDelete.length === 0) {
        return {
          deletedCount: 0,
          totalSizeFreed: 0,
          newStorageUsage: 0,
          newUsagePercentage: 0,
        };
      }

      // Track failed deletions
      const failedIds: string[] = [];
      const validFileIds = filesToDelete.map(file => file._id.toString());
      const totalSizeFreed = filesToDelete.reduce((sum, file) => sum + file.fileSize, 0);

      // Delete physical files
      for (const file of filesToDelete) {
        try {
          await this.deletePhysicalFile(file.fileKey);
        } catch (error) {
          this.logger.error(`Error deleting physical file ${file.fileKey}: ${error.message}`);
          failedIds.push(file._id.toString());
        }
      }

      // Update message references for successfully deleted files
      const successfulFiles = filesToDelete.filter(file => !failedIds.includes(file._id.toString()));
      if (successfulFiles.length > 0) {
        await this.updateMultipleMessageReferences(successfulFiles);
        
        // Track multiple file deletions in storage usage
        const deletions = successfulFiles.map(file => ({
          fileSize: file.fileSize,
          mediaType: file.mediaType,
        }));
        await this.storageService.trackMultipleFileDeletions(userId, deletions);
      }

      // Delete file metadata records that were successfully deleted
      const successfulFileIds = validFileIds.filter(id => !failedIds.includes(id));
      await this.fileMetadataService.deleteMultipleFileMetadata(successfulFileIds);

      // Get updated storage usage
      const updatedStorage = await this.storageService.calculateUserStorage(userId);

      return {
        deletedCount: successfulFileIds.length,
        totalSizeFreed,
        failedIds: failedIds.length > 0 ? failedIds : undefined,
        newStorageUsage: updatedStorage.totalUsage,
        newUsagePercentage: updatedStorage.usagePercentage,
      };
    } catch (error) {
      this.logger.error(`Error deleting multiple files for user ${userId}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Delete a physical file from storage
   * @param fileKey File key/path
   */
  private async deletePhysicalFile(fileKey: string): Promise<void> {
    try {
      // Determine if the file is in public or media directory
      let isPublic = false;
      if (fileKey.startsWith('profileImage-')) {
        isPublic = true;
      }

      // Construct file path
      const filePath = path.join(root.path, 'public', isPublic ? 'v-public' : 'media', fileKey);

      // Check if file exists
      if (fs.existsSync(filePath)) {
        // Delete the file
        fs.unlinkSync(filePath);
      } else {
        this.logger.warn(`File ${filePath} not found in storage`);
      }
    } catch (error) {
      this.logger.error(`Error deleting physical file ${fileKey}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Update message references when files are deleted
   * Removes file attachments from messages when files are deleted
   * @param fileMetadata File metadata containing message and file information
   */
  async updateMessageReferences(fileMetadata: IFileMetadata): Promise<void> {
    try {
      // Import MessageService dynamically to avoid circular dependency
      const { MessageService } = await import('../../../chat/message/message.service');
      const messageService = this.moduleRef.get(MessageService, { strict: false });

      if (!messageService) {
        this.logger.warn('MessageService not available, skipping message reference update');
        return;
      }

      // Get the current message to understand its structure
      const currentMessage = await messageService.findById(fileMetadata.messageId.toString());
      
      if (!currentMessage) {
        this.logger.warn(`Message ${fileMetadata.messageId} not found, skipping reference update`);
        return;
      }

      // Determine the appropriate replacement text based on media type
      const replacementText = this.getReplacementTextForMediaType(fileMetadata.mediaType);

      // Update the message to remove the file attachment and update content
      const updateData: any = {
        $unset: { msgAtt: 1 },
        $set: { 
          c: replacementText,
          isEdited: true
        }
      };

      await messageService.findByIdAndUpdate(fileMetadata.messageId.toString(), updateData);

      this.logger.log(`Updated message ${fileMetadata.messageId} to remove reference to deleted file ${fileMetadata.fileKey}`);
    } catch (error) {
      this.logger.error(`Error updating message references for file ${fileMetadata.fileKey}: ${error.message}`, error.stack);
      // Don't throw error as file deletion should still succeed even if message update fails
    }
  }

  /**
   * Get appropriate replacement text based on media type
   * @param mediaType Type of media that was deleted
   * @returns Replacement text for the message
   */
  private getReplacementTextForMediaType(mediaType: string): string {
    switch (mediaType.toLowerCase()) {
      case 'image':
        return '[Image deleted]';
      case 'video':
        return '[Video deleted]';
      case 'voice':
        return '[Voice message deleted]';
      case 'document':
        return '[Document deleted]';
      default:
        return '[File deleted]';
    }
  }

  /**
   * Update multiple message references when files are deleted in bulk
   * @param fileMetadataList Array of file metadata records
   */
  async updateMultipleMessageReferences(fileMetadataList: IFileMetadata[]): Promise<void> {
    try {
      // Import MessageService dynamically to avoid circular dependency
      const { MessageService } = await import('../../../chat/message/message.service');
      const messageService = this.moduleRef.get(MessageService, { strict: false });

      if (!messageService) {
        this.logger.warn('MessageService not available, skipping message reference updates');
        return;
      }

      // Group files by message ID to handle multiple files per message
      const messageUpdates = new Map<string, IFileMetadata[]>();
      
      for (const fileMetadata of fileMetadataList) {
        const messageId = fileMetadata.messageId.toString();
        if (!messageUpdates.has(messageId)) {
          messageUpdates.set(messageId, []);
        }
        messageUpdates.get(messageId)!.push(fileMetadata);
      }

      // Update each message
      for (const [messageId, files] of messageUpdates) {
        try {
          // If multiple files from same message, use generic text
          const replacementText = files.length > 1 
            ? '[Files deleted]' 
            : this.getReplacementTextForMediaType(files[0].mediaType);

          await messageService.findByIdAndUpdate(messageId, {
            $unset: { msgAtt: 1 },
            $set: { 
              c: replacementText,
              isEdited: true
            }
          });

          this.logger.log(`Updated message ${messageId} to remove references to ${files.length} deleted file(s)`);
        } catch (error) {
          this.logger.error(`Error updating message ${messageId}: ${error.message}`);
        }
      }
    } catch (error) {
      this.logger.error(`Error updating multiple message references: ${error.message}`, error.stack);
    }
  }
}