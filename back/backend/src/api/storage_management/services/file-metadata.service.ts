import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Schema } from 'mongoose';
import { IFileMetadata, MediaType } from '../entities/file_metadata.entity';
import { StorageService } from './storage.service';
import { fromBuffer } from 'file-type';

/**
 * Service for managing file metadata
 * Handles tracking and storing metadata for uploaded files
 */
@Injectable()
export class FileMetadataService {
  private readonly logger = new Logger(FileMetadataService.name);

  constructor(
    @InjectModel('file_metadata')
    private readonly fileMetadataModel: Model<IFileMetadata>,
    private readonly storageService: StorageService,
  ) {}

  /**
   * Create file metadata record and update storage usage
   * @param fileData File metadata to be stored
   * @returns Created file metadata record
   */
  async createFileMetadata(fileData: {
    userId: string | Schema.Types.ObjectId;
    fileKey: string;
    fileName: string;
    fileType: string;
    fileSize: number;
    mediaType: MediaType;
    messageId: string | Schema.Types.ObjectId;
    roomId: string | Schema.Types.ObjectId;
    senderId: string | Schema.Types.ObjectId;
    receiverId?: string | Schema.Types.ObjectId;
  }): Promise<IFileMetadata> {
    try {
      // Create file metadata record
      const fileMetadata = await this.fileMetadataModel.create({
        ...fileData,
        sentAt: new Date(),
      });

      // Update storage usage for the user
      await this.storageService.trackFileUpload(
        fileData.userId.toString(),
        fileData.fileSize,
        fileData.mediaType,
      );

      return fileMetadata;
    } catch (error) {
      this.logger.error(`Error creating file metadata: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Determine media type from file type
   * @param fileType MIME type of the file
   * @returns MediaType enum value
   */
  determineMediaType(fileType: string): MediaType {
    if (fileType.startsWith('image/')) {
      return MediaType.IMAGE;
    } else if (fileType.startsWith('video/')) {
      return MediaType.VIDEO;
    } else if (fileType.startsWith('audio/')) {
      return MediaType.VOICE;
    } else {
      return MediaType.DOCUMENT;
    }
  }

  /**
   * Get file metadata by file key
   * @param fileKey Unique file key
   * @returns File metadata record
   */
  async getFileMetadataByKey(fileKey: string): Promise<IFileMetadata | null> {
    try {
      return await this.fileMetadataModel.findOne({ fileKey });
    } catch (error) {
      this.logger.error(`Error getting file metadata by key: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get file metadata by message ID
   * @param messageId Message ID
   * @returns File metadata records
   */
  async getFileMetadataByMessageId(messageId: string | Schema.Types.ObjectId): Promise<IFileMetadata[]> {
    try {
      return await this.fileMetadataModel.find({ messageId });
    } catch (error) {
      this.logger.error(`Error getting file metadata by message ID: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get file metadata by room ID with pagination
   * @param roomId Room ID
   * @param page Page number (1-based)
   * @param limit Items per page
   * @returns File metadata records
   */
  async getFileMetadataByRoomId(
    roomId: string | Schema.Types.ObjectId,
    page = 1,
    limit = 20,
  ): Promise<{ files: IFileMetadata[]; total: number }> {
    try {
      const skip = (page - 1) * limit;
      const [files, total] = await Promise.all([
        this.fileMetadataModel
          .find({ roomId })
          .sort({ sentAt: -1 })
          .skip(skip)
          .limit(limit),
        this.fileMetadataModel.countDocuments({ roomId }),
      ]);

      return { files, total };
    } catch (error) {
      this.logger.error(`Error getting file metadata by room ID: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Delete file metadata by ID
   * @param fileId File metadata ID
   * @returns Deleted file metadata record
   */
  async deleteFileMetadata(fileId: string): Promise<IFileMetadata | null> {
    try {
      const fileMetadata = await this.fileMetadataModel.findByIdAndDelete(fileId);
      
      if (fileMetadata) {
        // Update storage usage after deletion
        await this.storageService.updateStorageUsage(fileMetadata.userId.toString());
      }
      
      return fileMetadata;
    } catch (error) {
      this.logger.error(`Error deleting file metadata: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Delete multiple file metadata records by IDs
   * @param fileIds Array of file metadata IDs
   * @returns Number of deleted records and total size freed
   */
  async deleteMultipleFileMetadata(fileIds: string[]): Promise<{ deletedCount: number; totalSizeFreed: number }> {
    try {
      // Get file metadata records to calculate size and get user IDs
      const filesToDelete = await this.fileMetadataModel.find({ _id: { $in: fileIds } });
      
      if (filesToDelete.length === 0) {
        return { deletedCount: 0, totalSizeFreed: 0 };
      }
      
      // Calculate total size freed
      const totalSizeFreed = filesToDelete.reduce((sum, file) => sum + file.fileSize, 0);
      
      // Delete files
      const deleteResult = await this.fileMetadataModel.deleteMany({ _id: { $in: fileIds } });
      
      // Get unique user IDs to update their storage usage
      const userIds = [...new Set(filesToDelete.map(file => file.userId.toString()))];
      
      // Update storage usage for each affected user
      await Promise.all(userIds.map(userId => this.storageService.updateStorageUsage(userId)));
      
      return { deletedCount: deleteResult.deletedCount, totalSizeFreed };
    } catch (error) {
      this.logger.error(`Error deleting multiple file metadata: ${error.message}`, error.stack);
      throw error;
    }
  }
}