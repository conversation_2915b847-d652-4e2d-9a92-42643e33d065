import { Controller, Get, Req, UseGuards, Query, Param, Delete, Body, HttpCode } from '@nestjs/common';
import { StorageService } from '../services/storage.service';
import { StorageUsageDto, StorageLimitStatusDto } from '../dto/storage-usage.dto';
import { VerifiedAuthGuard } from '../../../core/guards/verified.auth.guard';
import { GetFilesDto, PaginatedFilesDto, DeleteFilesDto, DeleteResultDto } from '../dto/file-management.dto';
import { MediaType } from '../entities/file_metadata.entity';
import { FileDeletionService } from '../services';

/**
 * Controller for storage management endpoints
 * Provides APIs for viewing storage usage and managing files
 */
@UseGuards(VerifiedAuthGuard)
@Controller('storage')
export class StorageController {
  constructor(
    private readonly storageService: StorageService,
    private readonly fileDeletionService: FileDeletionService
  ) {}

  /**
   * Get user's storage usage information
   * @param req Request object containing user information
   * @returns Storage usage details including total and categorized usage
   */
  @Get('usage')
  async getStorageUsage(@Req() req): Promise<StorageUsageDto> {
    const userId = req.user.userId;
    return this.storageService.calculateUserStorage(userId);
  }

  /**
   * Get detailed breakdown of storage usage by media type
   * @param req Request object containing user information
   * @returns Detailed storage usage information
   */
  @Get('usage/detailed')
  async getDetailedStorageUsage(@Req() req): Promise<StorageUsageDto> {
    const userId = req.user.userId;
    // Force recalculation of storage usage to ensure accuracy
    await this.storageService.updateStorageUsage(userId);
    return this.storageService.calculateUserStorage(userId);
  }

  /**
   * Check if user has reached storage limit
   * @param req Request object containing user information
   * @returns Storage limit status
   */
  @Get('limit')
  async checkStorageLimit(@Req() req): Promise<StorageLimitStatusDto> {
    const userId = req.user.userId;
    return this.storageService.checkStorageLimit(userId);
  }

  /**
   * Validate if a file of given size can be uploaded
   * @param req Request object containing user information
   * @param fileSize File size in bytes
   * @returns Success message if file can be uploaded
   */
  @Get('validate-upload/:fileSize')
  async validateFileUpload(
    @Req() req,
    @Param('fileSize') fileSize: number
  ): Promise<{ message: string }> {
    const userId = req.user.userId;
    await this.storageService.validateFileUpload(userId, Number(fileSize));
    return { message: 'File can be uploaded' };
  }

  /**
   * Get all user files with filtering and pagination
   * @param req Request object containing user information
   * @param query Query parameters for filtering and pagination
   * @returns Paginated list of files
   */
  @Get('files')
  async getUserFiles(
    @Req() req,
    @Query() query: GetFilesDto
  ): Promise<PaginatedFilesDto> {
    const userId = req.user.userId;
    return this.storageService.getUserFiles(userId, query);
  }

  /**
   * Get files of a specific media type
   * @param req Request object containing user information
   * @param mediaType Media type to filter by
   * @param page Page number
   * @param limit Items per page
   * @returns Paginated list of files of the specified media type
   */
  @Get('files/:mediaType')
  async getFilesByMediaType(
    @Req() req,
    @Param('mediaType') mediaType: MediaType,
    @Query('page') page?: number,
    @Query('limit') limit?: number
  ): Promise<PaginatedFilesDto> {
    const userId = req.user.userId;
    return this.storageService.getFilesByMediaType(
      userId,
      mediaType,
      page ? Number(page) : undefined,
      limit ? Number(limit) : undefined
    );
  }

  /**
   * Delete a single file by ID
   * @param req Request object containing user information
   * @param fileId File ID to delete
   * @returns Delete result with updated storage information
   */
  @Delete('files/:fileId')
  async deleteFile(
    @Req() req,
    @Param('fileId') fileId: string
  ): Promise<DeleteResultDto> {
    const userId = req.user.userId;
    return this.fileDeletionService.deleteFile(userId, fileId);
  }

  /**
   * Delete multiple files by IDs
   * @param req Request object containing user information
   * @param body Request body containing file IDs to delete
   * @returns Delete result with updated storage information
   */
  @Delete('files')
  @HttpCode(200)
  async deleteMultipleFiles(
    @Req() req,
    @Body() body: DeleteFilesDto
  ): Promise<DeleteResultDto> {
    const userId = req.user.userId;
    return this.fileDeletionService.deleteMultipleFiles(userId, body.fileIds);
  }
}