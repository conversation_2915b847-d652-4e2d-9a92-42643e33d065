import { Controller, Get, Req, UseGuards, Query, Param, Delete, Body, HttpCode } from '@nestjs/common';
import { StorageService } from '../services/storage.service';
import { StorageUsageDto, StorageLimitStatusDto } from '../dto/storage-usage.dto';
import { JwtAuthGuard } from '../../../core/guards/jwt-auth.guard';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiParam, ApiQuery } from '@nestjs/swagger';
import { GetFilesDto, PaginatedFilesDto, DeleteFilesDto, DeleteResultDto } from '../dto/file-management.dto';
import { MediaType } from '../entities/file_metadata.entity';
import { FileDeletionService } from '../services';

/**
 * Controller for storage management endpoints
 * Provides APIs for viewing storage usage and managing files
 */
@ApiTags('Storage Management')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
@Controller('storage')
export class StorageController {
  constructor(
    private readonly storageService: StorageService,
    private readonly fileDeletionService: FileDeletionService
  ) {}

  /**
   * Get user's storage usage information
   * @param req Request object containing user information
   * @returns Storage usage details including total and categorized usage
   */
  @Get('usage')
  @ApiOperation({ summary: 'Get user storage usage information' })
  @ApiResponse({ 
    status: 200, 
    description: 'Returns storage usage information including total and categorized usage',
    type: StorageUsageDto
  })
  async getStorageUsage(@Req() req): Promise<StorageUsageDto> {
    const userId = req.user.userId;
    return this.storageService.calculateUserStorage(userId);
  }

  /**
   * Get detailed breakdown of storage usage by media type
   * @param req Request object containing user information
   * @returns Detailed storage usage information
   */
  @Get('usage/detailed')
  @ApiOperation({ summary: 'Get detailed storage usage breakdown by media type' })
  @ApiResponse({ 
    status: 200, 
    description: 'Returns detailed storage usage breakdown by media type',
    type: StorageUsageDto
  })
  async getDetailedStorageUsage(@Req() req): Promise<StorageUsageDto> {
    const userId = req.user.userId;
    // Force recalculation of storage usage to ensure accuracy
    await this.storageService.updateStorageUsage(userId);
    return this.storageService.calculateUserStorage(userId);
  }

  /**
   * Check if user has reached storage limit
   * @param req Request object containing user information
   * @returns Storage limit status
   */
  @Get('limit')
  @ApiOperation({ summary: 'Check if user has reached storage limit' })
  @ApiResponse({
    status: 200,
    description: 'Returns storage limit status',
    type: StorageLimitStatusDto
  })
  async checkStorageLimit(@Req() req): Promise<StorageLimitStatusDto> {
    const userId = req.user.userId;
    return this.storageService.checkStorageLimit(userId);
  }

  /**
   * Validate if a file of given size can be uploaded
   * @param req Request object containing user information
   * @param fileSize File size in bytes
   * @returns Success message if file can be uploaded
   */
  @Get('validate-upload/:fileSize')
  @ApiOperation({ summary: 'Validate if a file can be uploaded based on size' })
  @ApiParam({ name: 'fileSize', description: 'File size in bytes', type: Number })
  @ApiResponse({
    status: 200,
    description: 'File can be uploaded'
  })
  @ApiResponse({
    status: 413,
    description: 'Storage limit would be exceeded'
  })
  async validateFileUpload(
    @Req() req,
    @Param('fileSize') fileSize: number
  ): Promise<{ message: string }> {
    const userId = req.user.userId;
    await this.storageService.validateFileUpload(userId, Number(fileSize));
    return { message: 'File can be uploaded' };
  }

  /**
   * Get all user files with filtering and pagination
   * @param req Request object containing user information
   * @param query Query parameters for filtering and pagination
   * @returns Paginated list of files
   */
  @Get('files')
  @ApiOperation({ summary: 'Get user files with filtering and pagination' })
  @ApiQuery({ name: 'mediaType', enum: MediaType, required: false })
  @ApiQuery({ name: 'page', type: Number, required: false })
  @ApiQuery({ name: 'limit', type: Number, required: false })
  @ApiQuery({ name: 'sortBy', enum: ['size', 'date'], required: false })
  @ApiQuery({ name: 'sortOrder', enum: ['asc', 'desc'], required: false })
  @ApiQuery({ name: 'startDate', type: Date, required: false })
  @ApiQuery({ name: 'endDate', type: Date, required: false })
  @ApiQuery({ name: 'roomId', type: String, required: false })
  @ApiQuery({ name: 'minSize', type: Number, required: false })
  @ApiQuery({ name: 'maxSize', type: Number, required: false })
  @ApiResponse({
    status: 200,
    description: 'Returns paginated list of files',
    type: PaginatedFilesDto
  })
  async getUserFiles(
    @Req() req,
    @Query() query: GetFilesDto
  ): Promise<PaginatedFilesDto> {
    const userId = req.user.userId;
    return this.storageService.getUserFiles(userId, query);
  }

  /**
   * Get files of a specific media type
   * @param req Request object containing user information
   * @param mediaType Media type to filter by
   * @param page Page number
   * @param limit Items per page
   * @returns Paginated list of files of the specified media type
   */
  @Get('files/:mediaType')
  @ApiOperation({ summary: 'Get files by media type' })
  @ApiParam({ name: 'mediaType', enum: MediaType, description: 'Media type to filter by' })
  @ApiQuery({ name: 'page', type: Number, required: false })
  @ApiQuery({ name: 'limit', type: Number, required: false })
  @ApiResponse({
    status: 200,
    description: 'Returns paginated list of files of the specified media type',
    type: PaginatedFilesDto
  })
  async getFilesByMediaType(
    @Req() req,
    @Param('mediaType') mediaType: MediaType,
    @Query('page') page?: number,
    @Query('limit') limit?: number
  ): Promise<PaginatedFilesDto> {
    const userId = req.user.userId;
    return this.storageService.getFilesByMediaType(
      userId,
      mediaType,
      page ? Number(page) : undefined,
      limit ? Number(limit) : undefined
    );
  }

  /**
   * Delete a single file by ID
   * @param req Request object containing user information
   * @param fileId File ID to delete
   * @returns Delete result with updated storage information
   */
  @Delete('files/:fileId')
  @ApiOperation({ summary: 'Delete a single file' })
  @ApiParam({ name: 'fileId', description: 'File ID to delete', type: String })
  @ApiResponse({
    status: 200,
    description: 'File deleted successfully',
    type: DeleteResultDto
  })
  @ApiResponse({
    status: 404,
    description: 'File not found or does not belong to the user'
  })
  async deleteFile(
    @Req() req,
    @Param('fileId') fileId: string
  ): Promise<DeleteResultDto> {
    const userId = req.user.userId;
    return this.fileDeletionService.deleteFile(userId, fileId);
  }

  /**
   * Delete multiple files by IDs
   * @param req Request object containing user information
   * @param body Request body containing file IDs to delete
   * @returns Delete result with updated storage information
   */
  @Delete('files')
  @HttpCode(200)
  @ApiOperation({ summary: 'Delete multiple files' })
  @ApiResponse({
    status: 200,
    description: 'Files deleted successfully',
    type: DeleteResultDto
  })
  async deleteMultipleFiles(
    @Req() req,
    @Body() body: DeleteFilesDto
  ): Promise<DeleteResultDto> {
    const userId = req.user.userId;
    return this.fileDeletionService.deleteMultipleFiles(userId, body.fileIds);
  }
}